import React from 'react';
import { RateLimitInfo, ModelInfo, AgentType } from '../types';

interface RateLimitModalProps {
  rateLimitInfo: RateLimitInfo;
  availableModels: ModelInfo[];
  onSwitchModel: (newModelId: string) => void;
  onPauseProject: () => void;
}

export const RateLimitModal: React.FC<RateLimitModalProps> = ({ 
  rateLimitInfo, 
  availableModels,
  onSwitchModel, 
  onPauseProject 
}) => {
  const { agentType, modelId, errorMessage, proposedAlternativeModelId } = rateLimitInfo;

  const currentModel = availableModels.find(m => m.id === modelId);
  const alternativeModel = proposedAlternativeModelId ? availableModels.find(m => m.id === proposedAlternativeModelId) : undefined;
  
  const getAgentDisplayName = (agent: AgentType): string => {
    return agent.charAt(0).toUpperCase() + agent.slice(1).toLowerCase().replace(/_/g, ' ');
  }

  return (
    <div className="fixed inset-0 bg-gray-900 bg-opacity-80 flex items-center justify-center p-4 z-50" role="alertdialog" aria-modal="true" aria-labelledby="rateLimitModalTitle" aria-describedby="rateLimitModalDescription">
      <div className="bg-gray-800 p-8 rounded-lg shadow-2xl w-full max-w-lg border border-yellow-500">
        <h2 id="rateLimitModalTitle" className="text-2xl font-bold mb-4 text-center text-yellow-400">Rate Limit Reached</h2>
        <div id="rateLimitModalDescription" className="text-gray-300 mb-6 space-y-3">
          <p>
            The <strong className="text-purple-300">{getAgentDisplayName(agentType)} Agent</strong>, using model 
            <strong className="text-cyan-300"> {currentModel?.name || modelId}</strong>, has encountered a rate limit.
          </p>
          <p className="text-sm text-gray-400 bg-gray-700 p-3 rounded-md">
            <strong>Error details:</strong> {errorMessage}
          </p>
          {alternativeModel && (
            <p>
              We suggest switching the <strong className="text-purple-300">{getAgentDisplayName(agentType)} Agent</strong> to model: 
              <strong className="text-green-400"> {alternativeModel.name}</strong> to continue.
            </p>
          )}
          {!alternativeModel && proposedAlternativeModelId && (
             <p className="text-orange-400">
              A preferred alternative model ({proposedAlternativeModelId}) could not be found in the available models list. You might need to update model configurations or wait.
            </p>
          )}
           {!alternativeModel && !proposedAlternativeModelId && (
             <p className="text-orange-400">
              No alternative models are configured for the {getAgentDisplayName(agentType)} Agent. You may need to wait for the rate limit to reset.
            </p>
          )}
        </div>
        <div className="flex flex-col sm:flex-row gap-4">
          {alternativeModel && (
            <button
              onClick={() => onSwitchModel(alternativeModel.id)}
              className="flex-1 bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-4 rounded-md transition duration-150 ease-in-out"
            >
              Switch to {alternativeModel.name} & Continue
            </button>
          )}
          <button
            onClick={onPauseProject}
            className="flex-1 bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-4 rounded-md transition duration-150 ease-in-out"
            aria-label={`Pause project and keep ${currentModel?.name || modelId} for ${getAgentDisplayName(agentType)} Agent`}
          >
            Pause Project (Keep {currentModel?.name || modelId})
          </button>
        </div>
         <p className="text-xs text-gray-500 mt-6 text-center">
          Pausing will save the current progress and model settings. You can attempt to resume later.
        </p>
      </div>
    </div>
  );
};

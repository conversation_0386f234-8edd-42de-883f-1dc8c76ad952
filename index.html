

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Autonomous CodeCrafters Inc.</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    /* Add Inter font from Google Fonts for better typography */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

    /* Custom scrollbar for webkit browsers */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #2d3748; /* gray-800 */
    }
    ::-webkit-scrollbar-thumb {
      background: #4a5568; /* gray-600 */
      border-radius: 4px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #718096; /* gray-500 */
    }
    /* Basic body styling for dark mode */
    body {
      color: #e2e8f0; /* gray-300 */
      font-family: 'Inter', sans-serif; /* A common sans-serif font */
      background: linear-gradient(45deg, #1a202c, #2d3748, #1a202c);
      background-size: 400% 400%;
      animation: gradientBG 15s ease infinite;
      overflow: hidden; /* Prevent body scroll when panels have their own */
    }

    html, body, #root {
      height: 100%; /* Ensure root elements take full height */
    }

    .app-container {
       display: flex;
       flex-direction: column;
       height: 100%;
    }

    main.flex-grow { /* Ensure main area can grow to fill space */
        flex-grow: 1;
        display: flex;
        min-height: 0; /* Necessary for flex children with overflow */
    }


    @keyframes gradientBG {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    /* Custom animation for active task border pulse */
    @keyframes pulse-border {
      0%, 100% { box-shadow: 0 0 0 0px rgba(168, 85, 247, 0.5); } /* purple-500 with opacity */
      50% { box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.2); }
    }
    .animate-pulse-border {
      animation: pulse-border 2s infinite ease-in-out;
    }

    /* Resizable Panel Sash Styling */
    .sash {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      z-index: 10;
      transition: background-color 0.2s ease-in-out;
    }
    .sash:focus {
        outline: none; /* Remove default focus outline */
    }
    
    /* Vertical Sash (for horizontal resizing) */
    .sash-vertical {
      width: 8px;
      cursor: col-resize;
    }
    .sash-vertical > div { /* The visible handle part */
      width: 2px;
      height: 30px;
      background-color: #4a5568; /* gray-600 */
      border-radius: 1px;
    }
    .sash-vertical:hover > div,
    .sash-vertical:focus > div {
      background-color: #a0aec0; /* gray-400 */
    }

    /* Horizontal Sash (for vertical resizing) */
    .sash-horizontal {
      height: 8px;
      cursor: row-resize;
    }
     .sash-horizontal > div { /* The visible handle part */
      height: 2px;
      width: 30px;
      background-color: #4a5568; /* gray-600 */
      border-radius: 1px;
    }
    .sash-horizontal:hover > div,
    .sash-horizontal:focus > div {
      background-color: #a0aec0; /* gray-400 */
    }


    /* Ensure panels within ResizablePanels manage their own scrolling */
    .resizable-panels-container > .panel {
        display: flex;
        flex-direction: column;
        overflow: hidden; /* Let child elements define scroll */
         /* Promote to own layer for smoother resizing */
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        transform: translateZ(0);
    }
    /* This attempts to target the direct scrollable child within a panel */
    .resizable-panels-container > .panel > *:first-child[class*="overflow-y-auto"],
    .resizable-panels-container > .panel > div:first-child[class*="overflow-y-auto"],
    .resizable-panels-container > .panel > .h-full.overflow-y-auto, /* common pattern */
    .resizable-panels-container > .panel > .flex-grow.overflow-y-auto /* common pattern */
     {
        flex-grow: 1;
        /* overflow-y: auto; is already assumed by the selector */
    }
    /* Fallback if no specific scrollable child is easily targetable, 
       the panel itself should allow its first direct child to grow and scroll if needed */
    .resizable-panels-container > .panel > :first-child {
        flex-grow: 1;
        overflow: auto; /* More generic overflow for fallback */
    }


  </style>
<script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "uuid": "https://esm.sh/uuid@^11.1.0",
    "@google/genai": "https://esm.sh/@google/genai@^0.14.0",
    "jszip": "https://esm.sh/jszip@3.10.1",
    "cors": "https://esm.sh/cors@^2.8.5",
    "express": "https://esm.sh/express@^5.1.0",
    "body-parser": "https://esm.sh/body-parser@^2.2.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body>
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>
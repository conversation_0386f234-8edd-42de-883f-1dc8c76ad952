
import { GoogleGenAI, GenerateContentResponse } from "@google/genai";
import {
  GeminiJsonPlannerResponse,
  GeminiJsonPlannerTask,
  FileNode,
  UserFeedback,
  GeminiJsonBuildValidationResponse,
  LicenseInfo,
  LicenseType
} from '../types';
import {
  SYSTEM_INSTRUCTION_PLANNER,
  SYSTEM_INSTRUCTION_FEEDBACK_TASK_GENERATOR,
  SYSTEM_INSTRUCTION_PLAN_REVIEWER,
  SYSTEM_INSTRUCTION_BUILD_VALIDATOR
} from '../constants';



class JsonParseError extends Error {
  public rawText: string;
  public attemptedJsonStr: string;
  public parserErrorMessage: string;

  constructor(message: string, rawText: string, attemptedJsonStr: string, parserErrorMessage: string) {
    super(message);
    this.name = "JsonParseError";
    this.rawText = rawText; 
    this.attemptedJsonStr = attemptedJsonStr; 
    this.parserErrorMessage = parserErrorMessage; 
  }
}

export class RateLimitError extends Error {
  public modelId: string;
  constructor(message: string, modelId: string) {
    super(message);
    this.name = "RateLimitError";
    this.modelId = modelId;
  }
}



// Helper function for validating GeminiJsonPlannerTask with new optional fields
const isValidGeminiJsonPlannerTask = (task: any): task is GeminiJsonPlannerTask => {
  return typeof task === 'object' && task !== null &&
    typeof task.description === 'string' &&
    (typeof task.details === 'undefined' || typeof task.details === 'string') &&
    (typeof task.id === 'undefined' || typeof task.id === 'string') &&
    (typeof task.priority === 'undefined' || ['low', 'medium', 'high'].includes(task.priority)) &&
    (typeof task.dependencies === 'undefined' || (Array.isArray(task.dependencies) && task.dependencies.every((dep: any) => typeof dep === 'string'))) &&
    (typeof task.estimatedComplexity === 'undefined' || ['low', 'medium', 'high', 'unknown'].includes(task.estimatedComplexity));
};


export class GeminiService {
  private ai: GoogleGenAI;
  private readonly DEFAULT_MAX_JSON_RETRIES = 2;
  private readonly RATE_LIMIT_MAX_RETRIES = 3;
  private readonly INITIAL_BACKOFF_MS = 3000;
  private readonly MAX_BACKOFF_MS = 60000;


  constructor(apiKey: string) {
    if (!apiKey) {
      throw new Error("API Key is required to initialize GeminiService.");
    }
    this.ai = new GoogleGenAI({ apiKey });
  }

  private parseJsonText(responseText: string): any {
    let jsonStr = responseText.trim();
    
    const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
    const match = jsonStr.match(fenceRegex);
    if (match && match[2]) {
      jsonStr = match[2].trim();
    }

    if (jsonStr.endsWith("```")) {
      jsonStr = jsonStr.slice(0, -3).trim();
    }

    let firstChar = -1;
    let lastChar = -1;
    let openChar: '{' | '[' | null = null;
    let closeChar: '}' | ']' | null = null;

    for (let i = 0; i < jsonStr.length; i++) {
        if (jsonStr[i] === '{') {
            firstChar = i;
            openChar = '{';
            closeChar = '}';
            break;
        }
        if (jsonStr[i] === '[') {
            firstChar = i;
            openChar = '[';
            closeChar = ']';
            break;
        }
    }
    
    let parseCandidateStr = jsonStr; 

    if (firstChar === -1 || !openChar || !closeChar) {
        // No clear JSON start, try to parse as is
    } else {
        let balance = 0;
        let inString = false;
        for (let i = firstChar; i < jsonStr.length; i++) {
            const char = jsonStr[i];

            if (char === '"') {
                // This is a simplified check; JSON.parse handles complex escapes.
                // We only care about not mistaking quotes inside strings for structural quotes.
                if (i > 0 && jsonStr[i-1] !== '\\') { 
                    inString = !inString;
                } else if (i === 0) { 
                    inString = !inString;
                }
            }

            if (!inString) {
                if (char === openChar) {
                    balance++;
                } else if (char === closeChar) {
                    balance--;
                }
            }

            if (balance === 0 && i >= firstChar) {
                lastChar = i;
                break;
            }
        }
        
        if (lastChar !== -1) {
            parseCandidateStr = jsonStr.substring(firstChar, lastChar + 1);
        } else {
            console.warn("Could not find balanced JSON structure, attempting to parse potentially partial or malformed JSON string (from first bracket/brace):", jsonStr.substring(firstChar, Math.min(firstChar + 200, jsonStr.length)));
            parseCandidateStr = jsonStr.substring(firstChar); 
        }
    }

    try {
      return JSON.parse(parseCandidateStr);
    } catch (e) {
      const specificParserError = e instanceof Error ? e.message : String(e);
      console.error(
        "Failed to parse JSON text. Specific Error:", specificParserError, 
        "\nOriginal response text (first 500 chars):", responseText.substring(0, 500), 
        "\nString attempted for parsing (first 500 chars):", parseCandidateStr.substring(0, 500)
      );
      throw new JsonParseError(
        `JSON parsing failed. Problematic string (first 200 chars): ${parseCandidateStr.substring(0,200)}... Specific error: ${specificParserError}`, 
        responseText, 
        parseCandidateStr, 
        specificParserError
      );
    }
  }

  private parseAndValidateJsonResponse<T>(response: GenerateContentResponse, schemaValidator: (data: any) => data is T, rawTextFromResponse?: string): T {
    const textToParse = rawTextFromResponse || response.text || '';
    const parsedData = this.parseJsonText(textToParse);

    if (schemaValidator(parsedData)) {
      return parsedData as T;
    } else {
      console.error("Parsed JSON does not match expected schema. Parsed data:", parsedData, "Original response text:", textToParse);
      throw new JsonParseError(
        "AI response JSON parsed but does not match expected data structure.", 
        textToParse,
        JSON.stringify(parsedData), 
        "Schema validation failed after successful parsing." 
      );
    }
  }

  public serializeFileStructureForPrompt(fileStructure: FileNode[], includeContent: boolean = false): string {
    let structureString = "Project File Structure Overview:\n";
    const traverse = (nodes: FileNode[], depth: number) => {
      for (const node of nodes) {
        let typeInfo = node.type;
        if (node.isTestFile) typeInfo += ' (test file)';
        
        structureString += `${"  ".repeat(depth)}- ${node.path} (${typeInfo}${node.type === 'file' && !node.content ? ', empty' : node.type === 'file' && node.content ? ', has content' : ''})\n`;
        if (includeContent && node.type === 'file' && node.content) {
            const previewContent = node.content.substring(0, 300) + (node.content.length > 300 ? "..." : "");
            structureString += `${"  ".repeat(depth+1)}Content Preview:\n${"  ".repeat(depth+2)}${previewContent.split('\n').join(`\n${"  ".repeat(depth+2)}`)}\n`;
        }
        if (node.children && node.children.length > 0) {
          traverse(node.children, depth + 1);
        }
      }
    };
    traverse(fileStructure, 0);
    return structureString;
  }

   private serializePlannerTasksForPrompt(tasks: GeminiJsonPlannerTask[]): string {
    if (!tasks || tasks.length === 0) {
      return "No tasks in the initial plan.\n";
    }
    let tasksString = "Initial Plan - Tasks Overview:\n";
    tasks.forEach((task, index) => {
      tasksString += `${index + 1}. Description: ${task.description}\n`;
      tasksString += `   Details (File Path): ${task.details || 'N/A'}\n`;
      if(task.priority) tasksString += `   Priority: ${task.priority}\n`;
      if(task.dependencies && task.dependencies.length > 0) tasksString += `   Dependencies: ${task.dependencies.join(', ')}\n`;
      if(task.estimatedComplexity) tasksString += `   Complexity: ${task.estimatedComplexity}\n`;
    });
    return tasksString;
  }


  public async makeRequestWithRetry<T>(
    modelName: string, 
    originalPrompt: string,
    systemInstruction: string,
    schemaValidator: (data: any) => data is T,
    temperature: number = 0.5,
    isCodeGenerationType: boolean = false 
  ): Promise<T> {
    let currentAttempt = 0; // Number of attempts made so far (0 for initial, 1 for first retry, etc.)
    let lastError: Error | JsonParseError | RateLimitError | null = null;

    let maxRetriesForCurrentErrorType = this.DEFAULT_MAX_JSON_RETRIES;

    while (currentAttempt <= maxRetriesForCurrentErrorType) {
      try {
        let promptForThisAttempt = originalPrompt;
        if (currentAttempt > 0 && lastError instanceof JsonParseError) { // This is a retry for JSON error
          console.warn(`Retry ${currentAttempt}/${maxRetriesForCurrentErrorType} for JSON processing with model ${modelName}. Requesting AI to correct previous malformed JSON or schema.`);
          const specificErrorMsg = lastError.parserErrorMessage;
          const problematicString = lastError.attemptedJsonStr; 
          
          let jsonIssueExplanation = "";
          let detectedInvalidStructure: any = null;
          try {
            detectedInvalidStructure = JSON.parse(problematicString); 
          } catch (e) { /* ignore if reparsing the problematic string itself fails */ }

          if (specificErrorMsg.includes("Schema validation failed")) {
              jsonIssueExplanation = "\nThe JSON was syntactically valid but did not match the expected data structure (schema).";
              if (isCodeGenerationType) { 
                  jsonIssueExplanation += "\nFor responses like code generation or linting, the expected JSON format is typically `{\"code\": \"your_generated_code_string\"}` or `{\"lintedCode\": \"...\"}`. If a clarification is needed by the Coder, it's `{\"code\": \"...\", \"clarificationQuestion\": \"...\"}`.";
                  if (detectedInvalidStructure && typeof detectedInvalidStructure === 'object' && 'files' in detectedInvalidStructure && Array.isArray(detectedInvalidStructure.files)) {
                      jsonIssueExplanation += "\nCRITICAL: Your previous response incorrectly used a `{\"files\": [...]}` structure. This is WRONG for single file operations (like Coder or Linter). You MUST return `{\"code\": \"...\"}` or similar for the ONE file specified in the request.";
                  } else if (detectedInvalidStructure && Array.isArray(detectedInvalidStructure)) {
                      jsonIssueExplanation += "\nCRITICAL: Your previous response was an array (e.g., `[{\"code\": \"...\"}]`). For single file operations, please return a single JSON object: `{\"code\": \"...\"}` or similar.";
                  }
              }
          } else { 
              jsonIssueExplanation = `
This means the JSON syntax itself was invalid. Common syntax errors include:
- Missing commas between elements or properties.
- Unmatched brackets [] or braces {}.
- **CRITICAL FOR STRING VALUES (like 'code', 'description', 'filePath', 'explanation', 'updatedContext', 'lintedCode', etc.): Incorrectly escaped special characters.**
  - Literal double quotes (\") inside strings MUST be escaped as \\".
  - Literal backslashes (\\) inside strings MUST be escaped as \\\\.
  - Newlines inside strings MUST be escaped as \\n. (Example: "line1\\nline2")
  - Tabs inside strings MUST be escaped as \\t. (Example: "item1\\titem2")
  - Other special characters like carriage return (\\r), form feed (\\f), and backspace (\\b) must also be correctly escaped if present.
Review the problematic JSON snippet and the specific error message ("${specificErrorMsg}") carefully to identify and fix the fundamental JSON syntax error.
The JSON must be PURE and STRICT, starting with { and ending with }, with no other text or markdown.`;
          }
          
          let errorContextSnippet = "";
          const errorPositionMatch = specificErrorMsg.match(/at position (\d+)/);
          const errorPosition = errorPositionMatch ? parseInt(errorPositionMatch[1], 10) : -1;
          const snippetRadius = 150; // Characters around the error position

          if (errorPosition !== -1 && problematicString.length > (snippetRadius * 2)) { // Only add if string is large enough and position is known
            const start = Math.max(0, errorPosition - snippetRadius);
            const end = Math.min(problematicString.length, errorPosition + snippetRadius);
            errorContextSnippet = `\n\nContext around error (approx. position ${errorPosition}):\n---\n...${problematicString.substring(start, end)}...\n---`;
          }


          promptForThisAttempt = `
            Your previous response to the original request resulted in a JSON processing issue.
            Original Request (Summary):
            ---
            ${originalPrompt.substring(0, 300)}... 
            ---
            Problematic JSON data (or snippet) from your previous attempt (string that caused parsing error):
            ---
            ${problematicString.substring(0, 800)} 
            ---
            ${errorContextSnippet}
            The SPECIFIC ISSUE encountered was: "${specificErrorMsg}"
            ${jsonIssueExplanation}

            Please re-evaluate the original request. Your entire response MUST be a single, perfectly valid JSON object that strictly adheres to all JSON syntax rules and matches the expected data structure for the task. Pay EXTREME attention to correctly escaping all special characters within string values. Do not include any explanatory text or markdown outside of the JSON object itself.
          `;
        } else if (currentAttempt > 0 && lastError) { 
             console.warn(`Retry ${currentAttempt}/${maxRetriesForCurrentErrorType} with model ${modelName} due to error: ${lastError.message}. Retrying.`);
        }

        const response = await this.ai.models.generateContent({
          model: modelName,
          contents: promptForThisAttempt,
          config: {
            systemInstruction: systemInstruction,
            responseMimeType: "application/json",
            temperature: temperature,
          }
        });
        
        const rawTextFromResponse = response.text || '';

        if (isCodeGenerationType) {
            const parsedData = this.parseJsonText(rawTextFromResponse);
            let objectToValidate: any = null;

            if (Array.isArray(parsedData)) {
                if (parsedData.length > 0 && typeof parsedData[0] === 'object' && parsedData[0] !== null && 
                    ( ('code' in parsedData[0] && typeof parsedData[0].code === 'string') || 
                      ('lintedCode' in parsedData[0] && typeof parsedData[0].lintedCode === 'string') ) ) {
                    console.warn(`AI returned an array of code/lint objects for model ${modelName}, using the first element.`);
                    objectToValidate = parsedData[0];
                } else if (parsedData.length === 0) {
                     throw new JsonParseError(`AI returned an empty array. Expected a code/lint object.`, rawTextFromResponse, "[]", "Expected non-empty array with code/lint object or a single code/lint object.");
                } else {
                    throw new JsonParseError(`AI returned an array, but its first element is not a valid code/lint object.`, rawTextFromResponse, JSON.stringify(parsedData), "Array element schema mismatch for code/lint object.");
                }
            } else {
                objectToValidate = parsedData;
            }

            if (schemaValidator(objectToValidate)) { 
                return objectToValidate as T;
            }
            const expectedSchemaMessage = `the expected JSON structure.`;

            throw new JsonParseError(`AI response JSON does not match expected schema (${expectedSchemaMessage}). Received: ${JSON.stringify(objectToValidate).substring(0,200)}`, rawTextFromResponse, JSON.stringify(objectToValidate), "Schema validation failed for code/lint object.");
        }
        return this.parseAndValidateJsonResponse<T>(response, schemaValidator, rawTextFromResponse);

      } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        console.error(`Attempt ${currentAttempt + 1}/${maxRetriesForCurrentErrorType + 1} with model ${modelName} failed:`, err.message);
        lastError = err;
        
        if (err.message && err.message.includes("429") && (err.message.toLowerCase().includes("resource_exhausted") || err.message.toLowerCase().includes("rate limit"))) {
            maxRetriesForCurrentErrorType = this.RATE_LIMIT_MAX_RETRIES; 
            console.warn(`Rate limit detected with model ${modelName}. Will retry up to ${maxRetriesForCurrentErrorType + 1} total times with increasing backoff.`);
            if (currentAttempt >= maxRetriesForCurrentErrorType) { 
                throw new RateLimitError(lastError.message, modelName);
            }
        } else if (err instanceof JsonParseError) {
            maxRetriesForCurrentErrorType = this.DEFAULT_MAX_JSON_RETRIES;
             if (currentAttempt >= maxRetriesForCurrentErrorType) {
                throw lastError;
            }
        } else { // Other errors
            maxRetriesForCurrentErrorType = 0; // No retries for unknown errors by default or fewer
             if (currentAttempt >= maxRetriesForCurrentErrorType) {
                throw lastError;
            }
        }
        
        currentAttempt++;
        let delay = this.INITIAL_BACKOFF_MS;
        if (maxRetriesForCurrentErrorType === this.RATE_LIMIT_MAX_RETRIES) { 
            delay = Math.min(this.MAX_BACKOFF_MS, this.INITIAL_BACKOFF_MS * Math.pow(2, currentAttempt -1)); // currentAttempt is 1-based for retries here
            delay += Math.random() * 1000; 
            console.log(`Rate limit backoff for model ${modelName}: waiting ${Math.round(delay/1000)}s before attempt ${currentAttempt + 1}/${maxRetriesForCurrentErrorType + 1}.`);
        } else if (lastError instanceof JsonParseError) { 
            delay = Math.min(this.MAX_BACKOFF_MS / 2, (this.INITIAL_BACKOFF_MS / 2) * Math.pow(2, currentAttempt -1));
            delay += Math.random() * 500; 
            console.log(`JSON error/schema backoff for model ${modelName}: waiting ${Math.round(delay/1000)}s before attempt ${currentAttempt + 1}/${maxRetriesForCurrentErrorType + 1}.`);
        } else { 
            delay = Math.min(this.MAX_BACKOFF_MS / 3, (this.INITIAL_BACKOFF_MS / 3) * Math.pow(2, currentAttempt -1));
            delay += Math.random() * 500;
            console.log(`General error backoff for model ${modelName}: waiting ${Math.round(delay/1000)}s before attempt ${currentAttempt + 1}/${maxRetriesForCurrentErrorType + 1}.`);
        }
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    // If loop finishes, it means all retries were exhausted for the last error type
    if (lastError instanceof RateLimitError) throw lastError; // Already a RateLimitError
    if (lastError?.message.includes("429")) throw new RateLimitError(lastError.message, modelName); // Convert to RateLimitError if it was one
    throw lastError || new Error(`Exhausted retries for AI request with model ${modelName}. Unknown error.`);
  }

  async generateInitialPlan(
    projectIdea: string, 
    modelName: string,
    projectContext?: string, 
    licenseInfo?: LicenseInfo   
  ): Promise<GeminiJsonPlannerResponse> {
    let originalPrompt = `Project Idea: "${projectIdea}"`;
    if (projectContext) {
      originalPrompt += `\n\nFull Project Context (for awareness, includes idea and license info if set):\n${projectContext}`;
    }
    if (licenseInfo) {
      originalPrompt += `\n\nLicense Information for this Project:
Type: ${licenseInfo.type}`;
      if (licenseInfo.type === LicenseType.Proprietary && licenseInfo.authorship) {
        originalPrompt += `
Author: ${licenseInfo.authorship.fullName}
Year: ${licenseInfo.authorship.copyrightYear}
Contact: ${licenseInfo.authorship.email}
${licenseInfo.authorship.website ? `Website: ${licenseInfo.authorship.website}` : ''}`;
      }
       originalPrompt += `\n(Ensure a task is created for the LICENSE file based on this.)`;
    } else {
        originalPrompt += `\n\nLicense Information: Not specified yet or default (e.g. MIT if not proprietary). Ensure a generic LICENSE file task if appropriate.`;
    }

    return this.makeRequestWithRetry<GeminiJsonPlannerResponse>(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_PLANNER,
      (data): data is GeminiJsonPlannerResponse => {
        return typeof data === 'object' && data !== null && 
               'tasks' in data && Array.isArray(data.tasks) && 
               ('fileStructure' in data ? Array.isArray(data.fileStructure) : true) && 
               data.tasks.every(isValidGeminiJsonPlannerTask) &&
               (!data.fileStructure || data.fileStructure.every((f: any) => typeof f === 'object' && f !== null && 'name' in f && 'type' in f)) &&
               (data.technologyStackSuggestion === null || typeof data.technologyStackSuggestion === 'undefined' || typeof data.technologyStackSuggestion === 'string');
      },
      0.3
    );
  }

  async reviewAndRefinePlan(
    projectIdea: string,
    initialTasks: GeminiJsonPlannerTask[], 
    initialFileStructure: Array<Omit<FileNode, 'id' | 'path' | 'children' | 'isTestFile'> & { children?: Array<Omit<FileNode, 'id' | 'path' | 'isTestFile'>> }>, 
    projectContext: string, 
    modelName: string,
    currentTechnologyStackSuggestion?: string 
  ): Promise<GeminiJsonPlannerResponse> {
    const initialTasksPrompt = this.serializePlannerTasksForPrompt(initialTasks);
    const initialFileStructurePrompt = `Initial Plan - File Structure Overview:\n${JSON.stringify(initialFileStructure, null, 2).substring(0, 2000)}...\n(Review and refine this structure if needed based on task adjustments.)`;
    const techStackPrompt = currentTechnologyStackSuggestion 
        ? `Current Technology Stack Suggestion to Review: ${currentTechnologyStackSuggestion}\n(Critically review this suggestion. If it's good, keep it or refine slightly. If it's inappropriate or missing and the idea is general, propose a new one and justify it. Ensure tasks and file structure align with the final stack.)` 
        : "No technology stack was initially suggested. If the project idea is general and one is appropriate, propose a common stack and justify it. Ensure tasks and file structure align.";

    const originalPrompt = `
      Original Project Idea: "${projectIdea}"
      ---
      Current Project Context (includes idea, license info, and any initial stack suggestion): 
      ${projectContext}
      ---
      Initial Plan to Review:
      ${initialTasksPrompt}
      ---
      ${initialFileStructurePrompt}
      ---
      ${techStackPrompt}
      ---
      Critically review this entire initial plan (tasks, file structure, and technology stack suggestion). 
      If it's good, return it as is or with minor tweaks (optionally with "reviewNotes"). 
      If it needs improvement (missing tasks, illogical structure, inappropriate/missing stack, etc.), provide a NEW, COMPLETE, and REFINED plan (new "tasks", "fileStructure", and "technologyStackSuggestion") and "reviewNotes" explaining changes.
      Ensure all essential boilerplate files are included for the project type and the (potentially new) technology stack. For tasks, ensure "id", "description", "details" are present and estimate "priority", "dependencies", and "estimatedComplexity".
      Ensure a task for the LICENSE file is present and correctly reflects the licenseInfo in the project context.
    `;
    return this.makeRequestWithRetry<GeminiJsonPlannerResponse>(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_PLAN_REVIEWER,
      (data): data is GeminiJsonPlannerResponse => {
        return typeof data === 'object' && data !== null &&
               'tasks' in data && Array.isArray(data.tasks) &&
               ('fileStructure' in data ? Array.isArray(data.fileStructure) : true) &&
               data.tasks.every(isValidGeminiJsonPlannerTask) &&
               (!data.fileStructure || data.fileStructure.every((f: any) => typeof f === 'object' && f !== null && 'name' in f && 'type' in f)) &&
               (data.reviewNotes === null || typeof data.reviewNotes === 'undefined' || typeof data.reviewNotes === 'string') &&
               (data.technologyStackSuggestion === null || typeof data.technologyStackSuggestion === 'undefined' || typeof data.technologyStackSuggestion === 'string');
      },
      0.4 
    );
  }


  async generateTasksFromUserFeedback(
    projectContext: string,
    projectIdea: string,
    fileStructure: FileNode[],
    feedback: UserFeedback,
    modelName: string 
  ): Promise<GeminiJsonPlannerResponse> { 
    const fileStructurePrompt = this.serializeFileStructureForPrompt(fileStructure);
    const originalPrompt = `
      Original Project Idea: ${projectIdea}
      User Feedback Received:
        Type: ${feedback.type}
        File Path (if any): ${feedback.filePath || 'N/A'}
        Description: ${feedback.description}
      ---
      Current Project Full Context (includes this feedback and licenseInfo): ${projectContext}
      ---
      ${fileStructurePrompt}
      ---
      Based on the user feedback, original idea, context, and file structure, generate a list of specific tasks to address the feedback. 
      For each task, include "id", "description", "details", and estimate "priority", "dependencies", and "estimatedComplexity".
      Ensure image-related feedback tasks have descriptions like "Generate image: ..." or "Update image: ..." and the filename in "details".
      If feedback pertains to licensing, generate appropriate tasks (e.g., "Update LICENSE file").
    `;
    return this.makeRequestWithRetry<GeminiJsonPlannerResponse>(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_FEEDBACK_TASK_GENERATOR,
      (data): data is GeminiJsonPlannerResponse => { 
        return typeof data === 'object' && data !== null &&
               'tasks' in data && Array.isArray(data.tasks) &&
               data.tasks.every(isValidGeminiJsonPlannerTask) && 
               !('fileStructure' in data); 
      },
      0.4
    );
  }











  
























  async validateProjectBuild(
    projectContext: string,
    fileStructure: FileNode[],
    packageJsonContent: string | undefined, 
    modelName: string
  ): Promise<GeminiJsonBuildValidationResponse> {
    const fileStructurePrompt = this.serializeFileStructureForPrompt(fileStructure, false); 
    const originalPrompt = `
      Project Context: ${projectContext}
      ---
      ${fileStructurePrompt}
      ---
      package.json content (if available):
      \`\`\`json
      ${packageJsonContent || "{ \"note\": \"package.json not found or empty\" }"}
      \`\`\`
      ---
      Perform a conceptual build check. 
      Infer project type. Analyze configuration files (package.json, tsconfig.json if implied by TS files). 
      Conceptually check for common type errors (if TypeScript) or Node.js startup issues.
      Report any significant issues that would likely prevent a build or cause runtime failure as "buildIssues".
      Provide "buildCommand", "projectType", and a "validationSummary".
    `;
    return this.makeRequestWithRetry<GeminiJsonBuildValidationResponse>(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_BUILD_VALIDATOR,
      (data): data is GeminiJsonBuildValidationResponse => {
        return typeof data === 'object' && data !== null &&
               'buildIssues' in data && Array.isArray(data.buildIssues) &&
               data.buildIssues.every((b: any) =>
                 typeof b === 'object' && b !== null &&
                 'filePath' in b && typeof b.filePath === 'string' &&
                 'bugId' in b && typeof b.bugId === 'string' &&
                 'description' in b && typeof b.description === 'string' &&
                 'severity' in b && typeof b.severity === 'string' &&
                 ['low', 'medium', 'high', 'critical'].includes(b.severity)
               ) &&
               (data.buildCommand === null || typeof data.buildCommand === 'undefined' || typeof data.buildCommand === 'string') &&
               (data.projectType === null || typeof data.projectType === 'undefined' || typeof data.projectType === 'string') &&
               'validationSummary' in data && typeof data.validationSummary === 'string';
      },
      0.3
    );
  }
}

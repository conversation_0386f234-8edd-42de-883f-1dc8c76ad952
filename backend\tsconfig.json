{"compilerOptions": {"target": "ES2020", "module": "ESNext", "lib": ["ES2020"], "outDir": "./dist-backend", "rootDir": "..", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "moduleResolution": "node", "sourceMap": true}, "include": ["../backend/server.ts", "../types.ts"], "exclude": ["../node_modules", "../dist-backend"]}
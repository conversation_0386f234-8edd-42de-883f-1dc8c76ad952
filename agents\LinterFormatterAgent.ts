import { GeminiService } from '../services/geminiService';
import { GeminiJsonLintFormatResponse, LoggingInterface, AgentType } from '../types';

/**
 * LinterFormatterAgent handles code linting and formatting.
 * Provides comprehensive logging of all linting and formatting activities and decisions.
 */
export class LinterFormatterAgent {
  private geminiService: GeminiService;
  private logger: LoggingInterface;

  constructor(geminiService: GeminiService, logger: LoggingInterface) {
    if (!geminiService) {
      throw new Error("LinterFormatterAgent: GeminiService instance is required.");
    }
    if (!logger) {
      throw new Error("LinterFormatterAgent: LoggingInterface instance is required.");
    }
    this.geminiService = geminiService;
    this.logger = logger;
  }

  /**
   * Logs company-level activities for this agent
   */
  private async logActivity(message: string, status: 'info' | 'success' | 'error' | 'working', taskId?: string): Promise<void> {
    await this.logger.addCompanyLog('Linter Formatter Agent', message, status, taskId);
  }

  /**
   * Logs decision-making activities for this agent
   */
  private logDecision(action: string, details: string, reason?: string, taskId?: string): void {
    this.logger.addDecisionLogEntry(AgentType.LINTER_FORMATTER, action, details, reason, taskId);
  }

  /**
   * Lints and formats code to improve consistency and readability.
   * @param code - The source code to lint and format.
   * @param filePath - The path of the file being processed.
   * @param projectContext - The current project context.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the lint and format response.
   */
  public async lintAndFormatCode(
    code: string,
    filePath: string,
    projectContext: string,
    modelName: string
  ): Promise<GeminiJsonLintFormatResponse> {
    try {
      await this.logActivity(`Starting linting and formatting for file: ${filePath} (${code.length} characters)`, 'working');
      this.logDecision('Linting Started', `Processing ${filePath} for formatting and linting improvements`, `Using model: ${modelName}`);

      const originalPrompt = `
        Project Context: ${projectContext}
        File Path: ${filePath}
        Code to lint and format:
        \`\`\`
        ${code}
        \`\`\`
        Refine the formatting and apply basic, safe linting corrections to this code. Do not change its logic.
        Return the improved code in the "lintedCode" field. Optionally, provide a brief "explanation".
      `;

      const response = await this.geminiService.makeRequestWithRetry(
        modelName,
        originalPrompt,
        "You are a Linter and Formatter Agent. Improve the code formatting and apply safe linting corrections without changing logic. Return a JSON object with 'lintedCode' property containing the improved code.",
        (data: any): data is GeminiJsonLintFormatResponse => {
          return typeof data === 'object' && data !== null &&
                 'lintedCode' in data && typeof data.lintedCode === 'string' &&
                 (data.explanation === null || typeof data.explanation === 'undefined' || typeof data.explanation === 'string');
        },
        0.3,
        true // isCodeGenerationType should be true as output structure is similar ({lintedCode: "..."})
      );

      await this.logActivity(`Linting and formatting completed for ${filePath} (${response.lintedCode.length} characters)`, 'success');
      this.logDecision('Linting Completed', `Processed ${filePath} successfully`, response.explanation ? `Changes: ${response.explanation.substring(0, 100)}${response.explanation.length > 100 ? '...' : ''}` : 'No explanation provided');

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Failed to lint and format ${filePath}: ${errorMessage}`, 'error');
      this.logDecision('Linting Failed', `Error processing ${filePath}: ${errorMessage}`, 'Linting encountered an error');
      console.error(`LinterFormatterAgent: Error linting and formatting code for "${filePath}" -`, error);
      throw error;
    }
  }
}

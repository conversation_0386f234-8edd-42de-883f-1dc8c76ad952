import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import { v4 as uuidv4 } from 'uuid';
import { ProjectContext, SavedProjectEntry, AgentLog, Task } from '../types.js';

const app = express();
const port = process.env.BACKEND_PORT || 3002;

app.use(cors());
app.use(express.json({ limit: '50mb' })); // Allow larger payloads for ProjectContext

// Request validation middleware
const validateProjectData = (req: Request, res: Response, next: NextFunction) => {
  if (req.method === 'POST' && req.path === '/api/projects') {
    const { body } = req;
    if (!body || typeof body !== 'object') {
      return res.status(400).json({ error: 'Request body is required and must be an object' });
    }

    if (!body.name || typeof body.name !== 'string' || body.name.trim().length === 0) {
      return res.status(400).json({ error: 'Project name is required and must be a non-empty string' });
    }

    if (!body.idea || typeof body.idea !== 'string' || body.idea.trim().length === 0) {
      return res.status(400).json({ error: 'Project idea is required and must be a non-empty string' });
    }
  }

  if (req.method === 'PUT' && req.path.startsWith('/api/projects/')) {
    const { body } = req;
    if (!body || typeof body !== 'object') {
      return res.status(400).json({ error: 'Request body is required and must be an object' });
    }
  }

  next();
};

// Apply validation middleware to project routes
app.use('/api/projects', validateProjectData);

// In-memory store
let projectsStore: Record<string, ProjectContext> = {};
let companyLogsStore: AgentLog[] = [];

// Helper to create SavedProjectEntry from ProjectContext
const createSavedEntry = (project: ProjectContext): SavedProjectEntry => ({
  id: project.id,
  name: project.name,
  lastModified: project.lastModified,
  ideaSnippet: project.idea.substring(0, 100) + (project.idea.length > 100 ? '...' : ''),
  taskCount: project.tasks.length,
  completedTaskCount: project.tasks.filter((t: Task) => t.status === 'completed').length,
});

// --- Project Endpoints ---
app.get('/api/projects', (_req: Request, res: Response) => {
  const savedEntries = Object.values(projectsStore)
    .map(createSavedEntry)
    .sort((a, b) => new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime());
  res.json(savedEntries);
});

app.post('/api/projects', (req: Request, res: Response) => {
  try {
    const project = req.body as ProjectContext;
    if (!project.id) {
      project.id = uuidv4(); // Assign an ID if not present
    }
    project.lastModified = new Date().toISOString();
    projectsStore[project.id] = project;
    console.log(`[Backend] Project created/received: ${project.id} - ${project.name}`);
    res.status(201).json({ id: project.id, name: project.name });
  } catch (error) {
    console.error('[Backend] Error creating project:', error);
    res.status(500).json({ error: 'Failed to create project' });
  }
});

app.get('/api/projects/:id', (req: Request, res: Response) => {
  const project = projectsStore[req.params.id];
  if (project) {
    res.json(project);
  } else {
    res.status(404).json({ message: 'Project not found' });
  }
});

app.put('/api/projects/:id', (req: Request, res: Response) => {
  try {
    const projectId = req.params.id;
    if (!projectId || typeof projectId !== 'string') {
      return res.status(400).json({ error: 'Valid project ID is required' });
    }

    if (!projectsStore[projectId]) {
      return res.status(404).json({ error: 'Project not found for update. Use POST to create.' });
    }

    const updatedProject = req.body as ProjectContext;
    updatedProject.id = projectId; // Ensure ID matches
    updatedProject.lastModified = new Date().toISOString();
    projectsStore[projectId] = updatedProject;
    console.log(`[Backend] Project updated: ${projectId} - ${updatedProject.name}`);
    res.json(updatedProject);
  } catch (error) {
    console.error('[Backend] Error updating project:', error);
    res.status(500).json({ error: 'Failed to update project' });
  }
});

app.delete('/api/projects/:id', (req: Request, res: Response) => {
  const projectId = req.params.id;
  if (projectsStore[projectId]) {
    const projectName = projectsStore[projectId].name;
    delete projectsStore[projectId];
    console.log(`[Backend] Project deleted: ${projectId} - ${projectName}`);
    res.status(204).send();
  } else {
    res.status(404).json({ message: 'Project not found' });
  }
});

// --- Log Endpoint ---
app.post('/api/logs', (req: Request, res: Response) => {
  try {
    const logEntry = req.body as AgentLog;

    // Enhanced validation for log entries
    if (!logEntry || typeof logEntry !== 'object') {
      return res.status(400).json({ error: 'Log entry must be an object' });
    }

    if (!logEntry.id || typeof logEntry.id !== 'string') {
      return res.status(400).json({ error: 'Log entry must have a valid id' });
    }

    if (!logEntry.message || typeof logEntry.message !== 'string') {
      return res.status(400).json({ error: 'Log entry must have a valid message' });
    }

    if (!logEntry.agent || typeof logEntry.agent !== 'string') {
      return res.status(400).json({ error: 'Log entry must have a valid agent' });
    }

    companyLogsStore.push(logEntry);
    if (companyLogsStore.length > 5000) { // Keep last 5000 logs
        companyLogsStore = companyLogsStore.slice(-5000);
    }
    // console.log(`[Backend] Log received: ${logEntry.agent} - ${logEntry.message.substring(0,50)}...`);
    res.status(201).json({ message: 'Log received' });
  } catch (error) {
    console.error('[Backend] Error processing log:', error);
    res.status(500).json({ error: 'Failed to process log entry' });
  }
});

// Basic error handler
app.use((err: Error, _req: Request, res: Response, _next: NextFunction) => {
  console.error("[Backend Error]", err.stack);
  res.status(500).send('Something broke on the backend!');
});

app.listen(port, () => {
  console.log(`[Backend] DevGenius Studio Backend listening on port ${port}`);
  console.log(`[Backend] Available Endpoints:
  GET    /api/projects
  POST   /api/projects
  GET    /api/projects/:id
  PUT    /api/projects/:id
  DELETE /api/projects/:id
  POST   /api/logs`);
});


import { AgentType } from '../types';

export const getAgentDisplayName = (agentType: AgentType | 'System' | 'ProjectManager' | 'User' | undefined | null): string => {
  if (!agentType || typeof agentType !== 'string') {
    return 'Unknown Agent'; // Fallback for undefined, null, or non-string types
  }

  if (agentType === 'System' || agentType === 'ProjectManager' || agentType === 'User') {
    return agentType;
  }
  // For AgentType enum values, which are strings like 'BUG_HUNTER'
  return agentType.charAt(0).toUpperCase() + agentType.slice(1).toLowerCase().replace(/_/g, ' ');
};

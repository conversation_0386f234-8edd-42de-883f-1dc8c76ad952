import { GeminiService } from '../services/geminiService';
import { GeminiJsonBugReportResponse, FileNode, LoggingInterface, AgentType } from '../types';

/**
 * BugHunterAgent handles bug detection and analysis in source code.
 * Provides comprehensive logging of all bug hunting activities and decisions.
 */
export class BugHunterAgent {
  private geminiService: GeminiService;
  private logger: LoggingInterface;

  constructor(geminiService: GeminiService, logger: LoggingInterface) {
    if (!geminiService) {
      throw new Error("BugHunterAgent: GeminiService instance is required.");
    }
    if (!logger) {
      throw new Error("BugHunterAgent: LoggingInterface instance is required.");
    }
    this.geminiService = geminiService;
    this.logger = logger;
  }

  /**
   * Logs company-level activities for this agent
   */
  private async logActivity(message: string, status: 'info' | 'success' | 'error' | 'working', taskId?: string): Promise<void> {
    await this.logger.addCompanyLog('Bug Hunter Agent', message, status, taskId);
  }

  /**
   * Logs decision-making activities for this agent
   */
  private logDecision(action: string, details: string, reason?: string, taskId?: string): void {
    this.logger.addDecisionLogEntry(AgentType.BUG_HUNTER, action, details, reason, taskId);
  }

  /**
   * Analyzes code for potential bugs and issues.
   * @param code - The source code to analyze.
   * @param filePath - The path of the file being analyzed.
   * @param projectContext - The current project context.
   * @param fileStructure - The current file structure of the project.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the bug report response.
   */
  public async analyzeCodeForBugs(
    code: string,
    filePath: string,
    projectContext: string,
    fileStructure: FileNode[],
    modelName: string
  ): Promise<GeminiJsonBugReportResponse> {
    try {
      await this.logActivity(`Starting bug analysis for file: ${filePath} (${code.length} characters)`, 'working');
      this.logDecision('Bug Analysis Started', `Analyzing ${filePath} for potential bugs`, `Using model: ${modelName}`);

      const fileStructurePrompt = this.geminiService.serializeFileStructureForPrompt(fileStructure);
      const originalPrompt = `
        Project Context: ${projectContext}
        ${fileStructurePrompt}
        File Path Being Analyzed: ${filePath}
        Code to analyze:
        \`\`\`
        ${code}
        \`\`\`
        Identify potential bugs or issues. Each bug must have a unique 'bugId'.
      `;

      const response = await this.geminiService.makeRequestWithRetry(
        modelName,
        originalPrompt,
        "You are a Bug Hunter Agent. Analyze the provided code for potential bugs and issues. Return a JSON object with 'bugs' array containing bug objects with 'filePath', 'bugId', 'description', and 'severity' properties.",
        (data: any): data is GeminiJsonBugReportResponse => {
          // Handle both {"bugs": [...]} and plain array [...] formats
          let bugsArray: any[];

          if (Array.isArray(data)) {
            // AI returned plain array format: [...]
            bugsArray = data;
          } else if (typeof data === 'object' && data !== null && 'bugs' in data && Array.isArray(data.bugs)) {
            // AI returned expected object format: {"bugs": [...]}
            bugsArray = data.bugs;
          } else {
            return false;
          }

          // Validate the bugs array structure
          return bugsArray.every((b: any) =>
            typeof b === 'object' && b !== null &&
            'filePath' in b && typeof b.filePath === 'string' &&
            'bugId' in b && typeof b.bugId === 'string' &&
            'description' in b && typeof b.description === 'string' &&
            'severity' in b && typeof b.severity === 'string' &&
            ['low', 'medium', 'high', 'critical'].includes(b.severity)
          );
        },
        0.4
      );

      // Normalize the response to always have the expected format
      let normalizedResponse: GeminiJsonBugReportResponse;
      if (Array.isArray(response)) {
        // Convert plain array to expected object format
        normalizedResponse = { bugs: response };
      } else {
        normalizedResponse = response;
      }

      await this.logActivity(`Bug analysis completed for ${filePath}. Found ${normalizedResponse.bugs.length} potential issues`, normalizedResponse.bugs.length > 0 ? 'info' : 'success');
      this.logDecision('Bug Analysis Completed', `Found ${normalizedResponse.bugs.length} potential bugs in ${filePath}`, `Analysis completed successfully`);

      return normalizedResponse;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Failed to analyze ${filePath} for bugs: ${errorMessage}`, 'error');
      this.logDecision('Bug Analysis Failed', `Error analyzing ${filePath}: ${errorMessage}`, 'Bug analysis encountered an error');
      console.error(`BugHunterAgent: Error analyzing code for bugs in "${filePath}" -`, error);
      throw error;
    }
  }
}

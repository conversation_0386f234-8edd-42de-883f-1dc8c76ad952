
import React from 'react';
import { FileNode } from '../types';
import { ExclamationCircleIcon } from './Icons'; // Import the icon

interface CodeViewerProps {
  file: FileNode | null;
}

const hasCriticalDependencyComment = (content?: string): boolean => 
  !!content && content.includes("// CRITICAL DEPENDENCY:");

export const CodeViewer: React.FC<CodeViewerProps> = ({ file }) => {
  if (!file) {
    return <p className="text-gray-500">Select a file from the explorer to view its content.</p>;
  }

  if (file.type === 'folder') {
    return <p className="text-gray-500">Selected item is a folder. Select a file to view content.</p>;
  }
  
  const languageClass = getLanguageClass(file.name);
  const isImageFile = file.content?.startsWith('data:image/');
  const containsCriticalDependency = hasCriticalDependencyComment(file.content);

  return (
    <div className="h-full flex flex-col">
      <h3 className="text-lg font-semibold text-yellow-400 mb-2 truncate flex items-center" title={file.path}>
        {file.name}
        {containsCriticalDependency && (
          <ExclamationCircleIcon className="w-5 h-5 text-yellow-400 ml-2 flex-shrink-0" title="Contains critical dependencies" />
        )}
      </h3>
      {containsCriticalDependency && (
        <div className="mb-2 p-2 bg-yellow-800/60 text-yellow-200 text-xs rounded-md flex items-center gap-2 border border-yellow-700">
          <ExclamationCircleIcon className="w-5 h-5 text-yellow-300 flex-shrink-0" />
          <span>This file may contain unresolved critical dependencies. Look for highlighted comments starting with "// CRITICAL DEPENDENCY:".</span>
        </div>
      )}
      <div className="bg-gray-900 p-3 rounded-md flex-grow overflow-auto">
        {isImageFile ? (
          <img 
            src={file.content} 
            alt={file.name} 
            className="max-w-full h-auto rounded-md shadow-lg" 
            aria-label={`Preview of ${file.name}`}
          />
        ) : (
          <pre className={`text-sm whitespace-pre-wrap break-all text-gray-300 ${languageClass}`}>
            <code>
            {file.content ? (
              file.content.split('\n').map((line, i, arr) => (
                <React.Fragment key={i}>
                  <span className={line.trim().startsWith('// CRITICAL DEPENDENCY:') ? 'bg-yellow-700/40 text-yellow-100 font-semibold px-1 rounded' : ''}>
                    {line}
                  </span>
                  {i < arr.length - 1 ? '\n' : ''}
                </React.Fragment>
              ))
            ) : (
              '// This file is empty or content is not yet generated.'
            )}
            </code>
          </pre>
        )}
      </div>
    </div>
  );
};

// Simple function to determine language
const getLanguageClass = (filename: string): string => {
  const extension = filename.split('.').pop()?.toLowerCase();
  switch (extension) {
    case 'js':
    case 'jsx':
      return 'language-javascript';
    case 'ts':
    case 'tsx':
      return 'language-typescript';
    case 'json':
      return 'language-json';
    case 'css':
      return 'language-css';
    case 'html':
      return 'language-html';
    case 'md':
      return 'language-markdown';
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'webp':
    case 'svg':
      return 'language-image'; 
    default:
      return 'language-text'; 
  }
};
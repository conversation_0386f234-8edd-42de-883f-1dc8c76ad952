# DevGenius Studio

DevGenius Studio is a comprehensive AI-powered development environment that helps you create, manage, and deploy software projects with the assistance of AI agents.

## Features

- **AI-Powered Project Planning**: Generate project plans and task breakdowns using AI
- **Multi-Agent System**: Different specialized AI agents for coding, testing, security analysis, and more
- **Project Management**: Create, save, and manage multiple projects
- **Real-time Development**: Live backend and frontend development with hot reload
- **File Structure Management**: Visual file tree and content management
- **Task Tracking**: Monitor project progress with detailed task status tracking

## Architecture

The application consists of:
- **Frontend**: React + TypeScript + Vite development server (port 5173)
- **Backend**: Express.js + TypeScript API server (port 3002)
- **AI Integration**: Google Gemini API for AI agent functionality

## Prerequisites

- **Node.js** (version 18 or higher)
- **npm** package manager
- **Google Gemini API Key** (for AI functionality)

## Installation & Setup

1. **Clone the repository** (if not already done)

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set up environment variables**:
   - Create a `.env.local` file in the root directory
   - Add your Gemini API key:
     ```
     GEMINI_API_KEY=your_gemini_api_key_here
     ```

4. **Run the development environment**:
   ```bash
   npm run dev
   ```

This will start both the frontend and backend servers concurrently.

## Available Scripts

- `npm run dev` - Start both frontend and backend development servers
- `npm run dev:frontend` - Start only the frontend development server (Vite)
- `npm run dev:backend` - Start only the backend development server (Express + TypeScript)
- `npm run build` - Build both frontend and backend for production
- `npm run build:frontend` - Build only the frontend
- `npm run build:backend` - Build only the backend
- `npm run lint` - Run ESLint on the codebase
- `npm run preview` - Preview the built frontend application

## Development Servers

When running `npm run dev`, the following servers will start:

- **Frontend**: http://localhost:5173/ (Vite development server)
- **Backend**: http://localhost:3002/ (Express API server)

### Backend API Endpoints

The backend provides the following REST API endpoints:

- `GET /api/projects` - Get all saved projects
- `POST /api/projects` - Create a new project
- `GET /api/projects/:id` - Get a specific project by ID
- `PUT /api/projects/:id` - Update a specific project
- `DELETE /api/projects/:id` - Delete a specific project
- `POST /api/logs` - Submit agent logs

## Project Structure

```
DevGenius_studio/
├── backend/
│   ├── server.ts          # Express server and API routes
│   └── tsconfig.json      # TypeScript config for backend
├── src/                   # Frontend React application
├── types.ts              # Shared TypeScript type definitions
├── package.json          # Dependencies and scripts
├── vite.config.ts        # Vite configuration
├── tsconfig.json         # TypeScript config for frontend
└── README.md             # This file
```

## Technology Stack

### Frontend
- **React 18** - UI framework
- **TypeScript** - Type safety
- **Vite** - Build tool and development server
- **Tailwind CSS** - Styling framework
- **ESLint** - Code linting

### Backend
- **Express.js** - Web framework
- **TypeScript** - Type safety
- **tsx** - TypeScript execution for development
- **nodemon** - Development file watching
- **CORS** - Cross-origin resource sharing

### AI Integration
- **Google Gemini API** - AI model for various agent functionalities

## Development Notes

- The backend uses ES modules (`"type": "module"` in package.json)
- TypeScript files are executed using `tsx` for development
- Both frontend and backend support hot reload during development
- The project uses a shared `types.ts` file for type definitions across frontend and backend

## Troubleshooting

If you encounter issues:

1. **Backend not starting**: Ensure all dependencies are installed with `npm install`
2. **API errors**: Check that your `GEMINI_API_KEY` is correctly set in `.env.local`
3. **Port conflicts**: Make sure ports 3002 (backend) and 5173 (frontend) are available
4. **TypeScript errors**: Run `npm run lint` to check for code issues

## Contributing

1. Make sure to follow the existing code style
2. Run `npm run lint` before committing
3. Test both frontend and backend functionality
4. Update this README if you add new features or change the setup process

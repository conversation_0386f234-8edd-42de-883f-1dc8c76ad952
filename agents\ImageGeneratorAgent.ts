import { GeminiService } from '../services/geminiService';
import { LoggingInterface, AgentType } from '../types';

/**
 * ImageGeneratorAgent handles image generation using AI models.
 * Provides comprehensive logging of all image generation activities and decisions.
 */
export class ImageGeneratorAgent {
  private geminiService: GeminiService;
  private logger: LoggingInterface;

  constructor(geminiService: GeminiService, logger: LoggingInterface) {
    if (!geminiService) {
      throw new Error("ImageGeneratorAgent: GeminiService instance is required.");
    }
    if (!logger) {
      throw new Error("ImageGeneratorAgent: LoggingInterface instance is required.");
    }
    this.geminiService = geminiService;
    this.logger = logger;
  }

  /**
   * Logs company-level activities for this agent
   */
  private async logActivity(message: string, status: 'info' | 'success' | 'error' | 'working', taskId?: string): Promise<void> {
    await this.logger.addCompanyLog('Image Generator Agent', message, status, taskId);
  }

  /**
   * Logs decision-making activities for this agent
   */
  private logDecision(action: string, details: string, reason?: string, taskId?: string): void {
    this.logger.addDecisionLogEntry(AgentType.IMAGE_GENERATOR, action, details, reason, taskId);
  }

  /**
   * Generates an image based on a text prompt using the Gemini image generation model.
   * @param prompt - The text prompt describing the image to generate.
   * @param modelName - The name of the Gemini image generation model to use.
   * @returns A promise that resolves to the generated image data with dataUrl, rawBytes, and mimeType.
   */
  public async generateImage(
    prompt: string,
    modelName: string,
    outputMimeType: 'image/png' | 'image/jpeg' = 'image/png'
  ): Promise<{ dataUrl: string, rawBytes: string, mimeType: string }> {
    try {
      await this.logActivity(`Starting image generation with prompt: "${prompt.substring(0, 50)}${prompt.length > 50 ? '...' : ''}"`, 'working');
      this.logDecision('Image Generation Started', `Prompt: ${prompt.substring(0, 100)}${prompt.length > 100 ? '...' : ''}`, `Using model: ${modelName}, Format: ${outputMimeType}`);

      // Since the original generateImageViaGemini method was removed, we need to implement it here
      let currentAttempt = 0;
      let lastError: Error | null = null;
      const maxRetries = 3;

      while (currentAttempt <= maxRetries) {
          try {
              if (currentAttempt > 0 && lastError) {
                   console.warn(`ImageGeneratorAgent: Retry ${currentAttempt}/${maxRetries} for image generation using model ${modelName} due to error: ${lastError.message}. Retrying.`);
              }

              const config: {
                numberOfImages: number;
                outputMimeType: 'image/png' | 'image/jpeg';
              } = {
                numberOfImages: 1,
                outputMimeType: outputMimeType
              };

              console.log(`ImageGeneratorAgent: Attempting image generation (attempt ${currentAttempt + 1}/${maxRetries + 1}) for model ${modelName} with prompt: "${prompt.substring(0,100)}..."`);

              const response = await (this.geminiService as any).ai.models.generateImages({
                  model: modelName,
                  prompt: prompt,
                  config: config,
              });

              if (!response.generatedImages || response.generatedImages.length === 0 || !response.generatedImages[0].image?.imageBytes) {
                  console.error("ImageGeneratorAgent: Gemini image generation response missing image data. Full response:", JSON.stringify(response).substring(0, 500));
                  throw new Error('Image generation failed: No image data received from API.');
              }

              const base64ImageBytes: string = response.generatedImages[0].image.imageBytes;
              const dataUrl = `data:${outputMimeType};base64,${base64ImageBytes}`;

              console.log(`ImageGeneratorAgent: Image generation for model ${modelName} complete. Image bytes length: ${base64ImageBytes?.length || 0}`);

              await this.logActivity(`Image generated successfully (${base64ImageBytes?.length || 0} bytes)`, 'success');
              this.logDecision('Image Generation Completed', `Generated image with ${base64ImageBytes?.length || 0} bytes`, 'Image generation completed successfully');

              return { dataUrl, rawBytes: base64ImageBytes, mimeType: outputMimeType };

          } catch (error) {
              const err = error instanceof Error ? error : new Error(String(error));
              console.error(`ImageGeneratorAgent: Image generation attempt ${currentAttempt + 1}/${maxRetries + 1} with model ${modelName} failed:`, err.message);
              lastError = err;
          }

          currentAttempt++;
          if (currentAttempt <= maxRetries) {
              const delay = Math.min(5000, 1000 * Math.pow(2, currentAttempt - 1));
              console.log(`ImageGeneratorAgent: Waiting ${Math.round(delay/1000)}s before attempt ${currentAttempt + 1}/${maxRetries + 1}.`);
              await new Promise(resolve => setTimeout(resolve, delay));
          }
      }

      const finalError = lastError || new Error(`ImageGeneratorAgent: Image generation failed for model ${modelName} after exhausting all ${maxRetries + 1} retries. Unknown error.`);
      await this.logActivity(`Image generation failed after ${maxRetries + 1} attempts: ${finalError.message}`, 'error');
      this.logDecision('Image Generation Failed', `Failed after ${maxRetries + 1} attempts: ${finalError.message}`, 'Image generation exhausted all retries');
      throw finalError;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Image generation error: ${errorMessage}`, 'error');
      this.logDecision('Image Generation Error', `Error: ${errorMessage}`, 'Image generation encountered an error');
      console.error(`ImageGeneratorAgent: Error generating image for prompt "${prompt.substring(0, 50)}..." -`, error);
      throw error;
    }
  }
}

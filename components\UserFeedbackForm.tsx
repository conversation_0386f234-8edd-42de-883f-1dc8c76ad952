import React, { useState } from 'react';
import { UserFeedback } from '../types'; 

interface UserFeedbackFormProps {
  onSubmit: (feedback: UserFeedback) => void;
  isLoading: boolean;
}

export const UserFeedbackForm: React.FC<UserFeedbackFormProps> = ({ onSubmit, isLoading }) => {
  const [type, setType] = useState<'bug' | 'feature' | 'change'>('bug');
  const [filePath, setFilePath] = useState('');
  const [description, setDescription] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (description.trim() && !isLoading) {
      onSubmit({
        type,
        filePath: filePath.trim() || undefined,
        description: description.trim(),
      });
      // Clear form after submission for better UX
      setType('bug');
      setFilePath('');
      setDescription('');
    }
  };

  return (
    <div className="bg-gray-800/50 p-4 rounded-lg shadow-xl mt-4 border border-purple-700">
      <h3 className="text-lg font-semibold mb-3 text-center text-purple-300">Provide Feedback or Request Changes</h3>
      <p className="text-gray-400 mb-4 text-xs text-center">
        The project has been verified. You can download it, or provide feedback for further iteration by the AI agents.
      </p>
      <form onSubmit={handleSubmit} className="space-y-3">
        <div>
          <label htmlFor="feedbackType" className="block text-xs font-medium text-gray-300 mb-1">Feedback Type</label>
          <select
            id="feedbackType"
            value={type}
            onChange={(e) => setType(e.target.value as 'bug' | 'feature' | 'change')}
            className="w-full p-2 bg-gray-700 border border-gray-600 rounded-md text-gray-200 text-sm focus:ring-1 focus:ring-purple-500 outline-none"
            disabled={isLoading}
            aria-label="Feedback type"
          >
            <option value="bug">Bug Report</option>
            <option value="feature">Feature Request</option>
            <option value="change">General Change</option>
          </select>
        </div>

        {type === 'bug' && (
          <div>
            <label htmlFor="filePath" className="block text-xs font-medium text-gray-300 mb-1">Affected File Path (Optional)</label>
            <input
              type="text"
              id="filePath"
              value={filePath}
              onChange={(e) => setFilePath(e.target.value)}
              placeholder="e.g., src/components/Header.tsx"
              className="w-full p-2 bg-gray-700 border border-gray-600 rounded-md text-gray-200 text-sm focus:ring-1 focus:ring-purple-500 outline-none"
              disabled={isLoading}
              aria-label="Affected file path for bug report"
            />
          </div>
        )}

        <div>
          <label htmlFor="description" className="block text-xs font-medium text-gray-300 mb-1">Description</label>
          <textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Describe the bug, feature, or change..."
            className="w-full p-2 bg-gray-700 border border-gray-600 rounded-md text-gray-200 text-sm focus:ring-1 focus:ring-purple-500 outline-none h-24 resize-none"
            required
            disabled={isLoading}
            aria-label="Description of feedback"
          />
        </div>

        <button
          type="submit"
          className="w-full bg-purple-600 hover:bg-purple-700 text-white font-semibold py-2.5 px-4 rounded-md text-sm transition duration-150 ease-in-out disabled:opacity-60 disabled:cursor-not-allowed"
          disabled={isLoading || !description.trim()}
        >
          {isLoading ? 'Submitting...' : 'Submit Feedback for AI Iteration'}
        </button>
      </form>
    </div>
  );
};
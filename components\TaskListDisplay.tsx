
import React, { useState, useMemo, useEffect } from 'react';
import { Task, TaskProcessingStage, TaskStatus, FileNode } from '../types';
import { 
    CheckCircleIcon, XCircleIcon, ClockIcon, PlayIcon, ExclamationCircleIcon, 
    ChevronDownIcon, ChevronRightIcon, SparklesIcon, MagnifyingGlassIcon, 
    DocumentTextIcon, ArchiveBoxIcon, ArrowPathIcon, ShieldExclamationIcon,
    BeakerIcon, ClipboardDocumentListIcon, LightBulbIcon, PhotoIcon,
    ArrowUpCircleIcon, ArrowDownCircleIcon, ArrowSmallRightIcon, PuzzlePieceIcon, ScaleIcon, QuestionMarkCircleIcon
} from './Icons';
import { MAX_ATTEMPTS_PER_BUG, MAX_BUG_FIXING_CYCLES_PER_TASK } from '../constants';

interface TaskListDisplayProps {
  tasks: Task[];
  activeTaskId: string | null;
  fileStructure: FileNode[];
  onFileSelect: (file: FileNode) => void;
  activeAgentAndModelString: string | null;
}

const getAllFilePaths = (nodes: FileNode[]): string[] => {
  const paths: string[] = [];
  const traverse = (currentNodes: FileNode[]) => {
    for (const node of currentNodes) {
      if (node.type === 'file' && typeof node.path === 'string') { // Ensure path is a string before pushing
        paths.push(node.path);
      }
      if (node.children) {
        traverse(node.children);
      }
    }
  };
  traverse(nodes);
  return paths;
};

function escapeRegExp(stringInput: string | undefined): string {
  if (typeof stringInput !== 'string') {
    return ''; // Return empty string if input is not a string (e.g., undefined)
  }
  return stringInput.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

interface ClickableTextProps {
  text: string;
  fileStructure: FileNode[];
  onFileSelect: (file: FileNode) => void;
  className?: string;
}

const ClickableText: React.FC<ClickableTextProps> = ({ text, fileStructure, onFileSelect, className }) => {
  const knownFilePaths = useMemo(() => getAllFilePaths(fileStructure), [fileStructure]);

  if (!text || knownFilePaths.length === 0) {
    return <span className={className}>{text}</span>;
  }

  const sortedPaths = [...knownFilePaths].sort((a, b) => b.length - a.length); 
  const regexPattern = sortedPaths.map(escapeRegExp).filter(s => s.length > 0).join('|');
  
  if (!regexPattern) { // If all paths were undefined/empty resulting in an empty pattern
    return <span className={className}>{text}</span>;
  }

  const regex = new RegExp(`(${regexPattern})`, 'g');
  const parts = text.split(regex);

  const findNode = (path: string): FileNode | null => {
    const find = (nodes: FileNode[]): FileNode | null => {
        for (const node of nodes) {
            if (node.path === path) return node;
            if (node.children) {
                const found = find(node.children);
                if (found) return found;
            }
        }
        return null;
    };
    return find(fileStructure);
  }

  return (
    <span className={className}>
      {parts.map((part, index) => {
        const matchedNode = knownFilePaths.includes(part) ? findNode(part) : null;
        if (matchedNode) {
          return (
            <button
              key={index}
              onClick={() => onFileSelect(matchedNode)}
              className="text-purple-400 hover:text-purple-300 underline focus:outline-none"
              title={`Open ${matchedNode.path}`}
              aria-label={`Open file ${matchedNode.path}`}
            >
              {part}
            </button>
          );
        }
        return <React.Fragment key={index}>{part}</React.Fragment>;
      })}
    </span>
  );
};

const getStageTextDisplay = (stage: TaskProcessingStage | undefined, currentStatus: TaskStatus): string => {
  if (currentStatus === 'completed') return 'Completed';
  if (currentStatus === 'error') {
    return stage ? `Failed: ${stage.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}` : 'Failed (Unknown Stage)';
  }
  if (!stage) {
    return 'Unknown Stage';
  }
  if (currentStatus === 'pending' && stage === 'QUEUED') return 'Queued';
  return stage.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
};

const getStageColor = (stage: TaskProcessingStage | undefined, currentStatus: TaskStatus): string => {
  if (currentStatus === 'error') return 'text-red-400';
  if (currentStatus === 'completed') return 'text-green-400';
  
  if (!stage) return 'text-gray-300'; // Color for unknown stage

  if (currentStatus === 'in-progress' || currentStatus === 'pending') {
    switch (stage) {
      case 'QUEUED': return 'text-gray-400';
      case 'CODING': return 'text-blue-400';
      case 'LINTING_FORMATTING': return 'text-teal-400';
      case 'SECURITY_ANALYSIS': return 'text-red-500';
      case 'IMAGE_GENERATING': return 'text-pink-400';
      case 'BUG_CHECKING': return 'text-yellow-400';
      case 'REFACTORING': return 'text-orange-400';
      case 'AWAITING_BUG_RECHECK': return 'text-teal-400';
      case 'CONTEXT_UPDATING': return 'text-indigo-400';
      case 'TEST_PLAN_GENERATING': case 'TEST_PLANNING_INITIALIZING': return 'text-cyan-400';
      case 'TEST_CODE_WRITING': case 'TEST_CODE_INITIALIZING': return 'text-lime-400';
      case 'TEST_ANALYSIS_RUNNING': case 'TEST_ANALYSIS_INITIALIZING': return 'text-purple-400';
      case 'CLARIFICATION_NEEDED': return 'text-fuchsia-400';
      case 'CLARIFICATION_PROVIDED': return 'text-fuchsia-300';
      default: return 'text-gray-300';
    }
  }
  return 'text-gray-400';
};

const StageIconInternal: React.FC<{ stage: TaskProcessingStage | undefined, status: TaskStatus }> = ({ stage, status }) => {
  if (status === 'error') return <XCircleIcon className="w-5 h-5 text-red-400" title="Error" />;
  if (status === 'completed') return <CheckCircleIcon className="w-5 h-5 text-green-400" title="Completed" />;
  
  if (!stage) return <ClockIcon className="w-5 h-5 text-gray-500" title="Unknown Stage" />;

  if (status === 'in-progress' || status === 'pending') {
     switch (stage) {
      case 'QUEUED':
        return <ClockIcon className="w-5 h-5 text-gray-500" title="Queued" />;
      case 'CODING':
        return <PlayIcon className="w-5 h-5 text-blue-400 animate-pulse" title="Coding in progress" />;
      case 'CODE_GENERATED':
        return <DocumentTextIcon className="w-5 h-5 text-blue-300" title="Code generated" />;
      case 'LINTING_FORMATTING':
        return <SparklesIcon className="w-5 h-5 text-teal-400 animate-pulse" title="Linting & Formatting" />;
      case 'SECURITY_ANALYSIS':
        return <ShieldExclamationIcon className="w-5 h-5 text-red-500 animate-pulse" title="Security Analysis" />;
      case 'IMAGE_GENERATING':
        return <PhotoIcon className="w-5 h-5 text-pink-400 animate-pulse" title="Image generating" />;
      case 'IMAGE_GENERATED':
        return <PhotoIcon className="w-5 h-5 text-pink-300" title="Image generated" />;
      case 'BUG_CHECKING':
        return <MagnifyingGlassIcon className="w-5 h-5 text-yellow-400 animate-pulse" title="Bug checking" />;
      case 'BUGS_ANALYZED':
        return <MagnifyingGlassIcon className="w-5 h-5 text-yellow-300" title="Bug analysis complete" />;
      case 'REFACTORING':
        return <ArrowPathIcon className="w-5 h-5 text-orange-400 animate-spin" title="Refactoring code" />;
      case 'AWAITING_BUG_RECHECK':
        return <ShieldExclamationIcon className="w-5 h-5 text-teal-400 animate-pulse" title="Awaiting bug re-check" />;
      case 'CONTEXT_UPDATING':
        return <ArchiveBoxIcon className="w-5 h-5 text-indigo-400 animate-pulse" title="Updating project memory" />;
      case 'CONTEXT_UPDATED':
        return <ArchiveBoxIcon className="w-5 h-5 text-indigo-300" title="Project memory updated" />;
      case 'TEST_PLANNING_INITIALIZING':
      case 'TEST_PLAN_GENERATING':
        return <LightBulbIcon className="w-5 h-5 text-cyan-400 animate-pulse" title="Test Planning" />;
      case 'TEST_PLAN_GENERATED':
         return <LightBulbIcon className="w-5 h-5 text-cyan-300" title="Test Plan Generated" />;
      case 'TEST_CODE_INITIALIZING':
      case 'TEST_CODE_WRITING':
        return <ClipboardDocumentListIcon className="w-5 h-5 text-lime-400 animate-pulse" title="Writing Test Code" />;
      case 'TEST_CODE_GENERATED':
        return <ClipboardDocumentListIcon className="w-5 h-5 text-lime-300" title="Test Code Generated" />;
      case 'TEST_ANALYSIS_INITIALIZING':
      case 'TEST_ANALYSIS_RUNNING':
        return <BeakerIcon className="w-5 h-5 text-purple-400 animate-pulse" title="Analyzing Tests" />;
      case 'TEST_ANALYSIS_COMPLETED':
         return <BeakerIcon className="w-5 h-5 text-purple-300" title="Test Analysis Complete" />;
      case 'CLARIFICATION_NEEDED':
         return <QuestionMarkCircleIcon className="w-5 h-5 text-fuchsia-400 animate-pulse" title="Clarification Needed" />;
      case 'CLARIFICATION_PROVIDED':
         return <QuestionMarkCircleIcon className="w-5 h-5 text-fuchsia-300" title="Clarification Provided" />;
      case 'DONE': // Explicitly handle DONE, though completed status might override
        return <CheckCircleIcon className="w-5 h-5 text-green-400" title="Done" />;
      default: // stage is a known string from TaskProcessingStage but not specifically cased above
        return <PlayIcon className="w-5 h-5 text-gray-400 animate-pulse" title={`Processing: ${stage.toLowerCase()}`} />;
    }
  }
  return <ClockIcon className="w-5 h-5 text-gray-500" title="Pending" />; 
};


const PriorityIcon: React.FC<{ priority?: 'low' | 'medium' | 'high' }> = ({ priority }) => {
  switch (priority) {
    case 'high':
      return <ArrowUpCircleIcon className="w-3.5 h-3.5 text-red-400" title="High Priority" />;
    case 'medium':
      return <ArrowSmallRightIcon className="w-3.5 h-3.5 text-yellow-400" title="Medium Priority" />;
    case 'low':
      return <ArrowDownCircleIcon className="w-3.5 h-3.5 text-green-400" title="Low Priority" />;
    default:
      return null;
  }
};

const ComplexityIcon: React.FC<{ complexity?: 'low' | 'medium' | 'high' | 'unknown' }> = ({ complexity }) => {
    switch (complexity) {
      case 'high':
        return <PuzzlePieceIcon className="w-3.5 h-3.5 text-red-400" title="High Complexity" />;
      case 'medium':
        return <PuzzlePieceIcon className="w-3.5 h-3.5 text-yellow-400" title="Medium Complexity" />;
      case 'low':
        return <PuzzlePieceIcon className="w-3.5 h-3.5 text-green-400" title="Low Complexity" />;
      default:
        return <PuzzlePieceIcon className="w-3.5 h-3.5 text-gray-400" title="Unknown Complexity" />;
    }
};


interface TaskItemProps {
  task: Task;
  isActive: boolean;
  fileStructure: FileNode[];
  onFileSelect: (file: FileNode) => void;
  currentActiveAgentInfo: string | null;
}

const TaskItem: React.FC<TaskItemProps> = ({ task, isActive, fileStructure, onFileSelect, currentActiveAgentInfo }) => {
  const [isOpen, setIsOpen] = useState(isActive);

  useEffect(() => {
    setIsOpen(isActive);
  }, [isActive]);
  
  const currentBugInfoText = () => {
    if (task.currentProcessingStage === 'REFACTORING' && task.currentRefactoringBugId) {
        const bugToDisplay = task.unresolvedBugs?.find(b => b.bugId === task.currentRefactoringBugId);
        if (bugToDisplay) {
            const attemptNumber = bugToDisplay.attempts || 0;
            return `Targeting Bug: "${bugToDisplay.description.substring(0, 70)}${bugToDisplay.description.length > 70 ? '...' : ''}" (ID: ${bugToDisplay.bugId}, Path: ${bugToDisplay.filePath}, Attempt ${attemptNumber}/${MAX_ATTEMPTS_PER_BUG})`;
        }
    }
    return null;
  };
  const bugInfoText = currentBugInfoText();


  const taskContainerClasses = `
    p-3 rounded-md shadow-md transition-all duration-300 ease-in-out
    ${isActive ? 'ring-2 ring-purple-500 bg-gray-700/80 animate-pulse-border' : 'bg-gray-700/50'} 
    ${task.status === 'completed' ? 'opacity-70 border-l-4 border-green-600' : 
      task.status === 'error' ? 'border-l-4 border-red-600' : 
      'border-l-4 border-gray-600 hover:bg-gray-600/70'
    }`;

  return (
    <div className={taskContainerClasses}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3 min-w-0">
          <StageIconInternal stage={task.currentProcessingStage} status={task.status} />
          <div className="flex-grow min-w-0">
            <span className={`font-medium truncate block ${task.status === 'completed' ? 'line-through text-gray-500' : 'text-gray-100'}`}>
              {task.description}
            </span>
            <div className="flex items-center gap-2 mt-0.5 flex-wrap">
                <span className={`text-xs ${getStageColor(task.currentProcessingStage, task.status)}`}>
                  {getStageTextDisplay(task.currentProcessingStage, task.status)}
                </span>
                {task.priority && (
                    <span className="flex items-center text-xs text-gray-400" title={`Priority: ${task.priority}`}>
                        <PriorityIcon priority={task.priority} />
                        <span className="ml-0.5 hidden sm:inline">{task.priority}</span>
                    </span>
                )}
                {task.estimatedComplexity && (
                    <span className="flex items-center text-xs text-gray-400" title={`Complexity: ${task.estimatedComplexity}`}>
                        <ComplexityIcon complexity={task.estimatedComplexity} />
                         <span className="ml-0.5 hidden sm:inline">{task.estimatedComplexity}</span>
                    </span>
                )}
                {task.dependencies && task.dependencies.length > 0 && (
                    <span className="flex items-center text-xs text-gray-400" title={`Dependencies: ${task.dependencies.length}`}>
                        <ScaleIcon className="w-3.5 h-3.5 text-blue-400" />
                        <span className="ml-0.5 hidden sm:inline">{task.dependencies.length} dep(s)</span>
                    </span>
                )}
                {task.status === 'in-progress' && currentActiveAgentInfo && (
                  <span className="text-xs text-purple-300 font-semibold animate-pulse ml-1 sm:ml-2 truncate" title={currentActiveAgentInfo}>
                    ({currentActiveAgentInfo}...)
                  </span>
                )}
            </div>
            {(task.unresolvedBugs && task.unresolvedBugs.length > 0 && task.status !== 'completed') ? (
              <span className="text-xs text-orange-300 ml-2">
                (Unresolved Bugs: {task.unresolvedBugs?.length || 0}, Cycles: {task.bugFixingCycles || 0}/{MAX_BUG_FIXING_CYCLES_PER_TASK})
              </span>
            ) : (task.status === 'in-progress' && (task.currentProcessingStage === 'REFACTORING' || task.currentProcessingStage === 'AWAITING_BUG_RECHECK' || task.currentProcessingStage === 'BUGS_ANALYZED')) ? (
               <span className="text-xs text-gray-400 ml-2">
                (Fix Cycles: {task.bugFixingCycles || 0}/{MAX_BUG_FIXING_CYCLES_PER_TASK})
              </span>
            ) : null}
          </div>
        </div>
        {task.agentMessages.length > 0 && (
           <button onClick={() => setIsOpen(!isOpen)} className="p-1 rounded hover:bg-gray-600" aria-label={isOpen ? "Collapse agent notes" : "Expand agent notes"}>
            {isOpen ? <ChevronDownIcon className="w-4 h-4 text-gray-400" /> : <ChevronRightIcon className="w-4 h-4 text-gray-400" />}
          </button>
        )}
      </div>
      {task.details && <p className="text-xs text-gray-400 mt-1 ml-8 truncate" title={task.details}>Target: <ClickableText text={task.details} fileStructure={fileStructure} onFileSelect={onFileSelect} /></p>}
      {task.purpose && (
        <p className="text-xs text-sky-300 mt-1 ml-8" title={task.purpose}>
          Purpose: <ClickableText text={task.purpose} fileStructure={fileStructure} onFileSelect={onFileSelect} className="text-sky-300" />
        </p>
      )}
      {task.relatedSourceFiles && task.relatedSourceFiles.length > 0 && (
         <p className="text-xs text-gray-500 mt-1 ml-8 truncate" title={task.relatedSourceFiles.join(', ')}>Sources: <ClickableText text={task.relatedSourceFiles.join(', ')} fileStructure={fileStructure} onFileSelect={onFileSelect} /></p>
      )}
      {bugInfoText && (
        <p className="text-xs text-yellow-300 mt-1 ml-8 italic">
          <ClickableText text={bugInfoText} fileStructure={fileStructure} onFileSelect={onFileSelect} className="text-yellow-300" />
        </p>
      )}
      {task.clarificationQuestion && task.currentProcessingStage === 'CLARIFICATION_NEEDED' && (
        <p className="text-xs text-fuchsia-300 mt-1 ml-8 italic">
          Q: {task.clarificationQuestion}
        </p>
      )}
      {task.clarifierResponse && task.currentProcessingStage === 'CLARIFICATION_PROVIDED' && (
        <p className="text-xs text-fuchsia-300 mt-1 ml-8 italic">
          A: {task.clarifierResponse}
        </p>
      )}
      {task.error && (
        <div className="mt-2 p-2 bg-red-800 bg-opacity-60 rounded text-red-300 text-xs flex items-start gap-2">
          <ExclamationCircleIcon className="w-4 h-4 mt-0.5 flex-shrink-0" />
          <ClickableText text={task.error} fileStructure={fileStructure} onFileSelect={onFileSelect} />
        </div>
      )}
      {isOpen && task.agentMessages.length > 0 && (
        <div className="mt-2 ml-8 pl-3 border-l-2 border-gray-600 space-y-1.5 max-h-60 overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
          <h4 className="text-xs font-semibold text-purple-300 mb-1 sticky top-0 bg-gray-700/80 py-1 z-10">Agent Notes:</h4>
          {task.agentMessages.slice().reverse().map(msg => (
            <div key={msg.id} className={`text-[0.7rem] p-1.5 rounded-md bg-gray-800/60 shadow-sm ${msg.status === 'error' ? 'text-red-300 border-l-2 border-red-500' : msg.status === 'success' ? 'text-green-300 border-l-2 border-green-500' : 'text-gray-400 border-l-2 border-gray-500'}`}>
              <div className="flex justify-between items-center mb-0.5">
                <span className="font-semibold">{msg.agent}</span>
                <span className="text-[0.65rem] text-gray-500">{new Date(msg.timestamp).toLocaleTimeString()}</span>
              </div>
              {msg.stage && <p className="text-[0.65rem] text-purple-300 mb-0.5">Stage: {msg.stage.replace(/_/g, ' ').toLowerCase()}</p>}
              <p className="mb-1"><ClickableText text={msg.message} fileStructure={fileStructure} onFileSelect={onFileSelect} /></p>
              {msg.subDetailSections && msg.subDetailSections.length > 0 && (
                <div className="mt-1.5 space-y-1 border-t border-gray-700 pt-1.5">
                  {msg.subDetailSections.map((section, idx) => (
                    <div key={idx} className="text-[0.65rem]">
                      <strong className="text-gray-300 block mb-0.5">{section.title}:</strong>
                      {section.isCodeBlock ? (
                        <pre className="bg-gray-900 p-1.5 rounded-sm text-gray-300 text-[0.6rem] overflow-x-auto scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-gray-900">
                           <code><ClickableText text={section.content} fileStructure={fileStructure} onFileSelect={onFileSelect} /></code>
                        </pre>
                      ) : (
                        <p className="whitespace-pre-wrap break-words"><ClickableText text={section.content} fileStructure={fileStructure} onFileSelect={onFileSelect} /></p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export const TaskListDisplay: React.FC<TaskListDisplayProps> = ({ tasks, activeTaskId, fileStructure, onFileSelect, activeAgentAndModelString }) => {
  if (tasks.length === 0) {
    return <p className="text-gray-500 text-center italic">No tasks planned yet. Awaiting project commission or next phase.</p>;
  }

  return (
    <div className="space-y-2.5">
      {tasks.map((task) => (
        <TaskItem 
          key={task.id} 
          task={task} 
          isActive={task.id === activeTaskId} 
          fileStructure={fileStructure}
          onFileSelect={onFileSelect}
          currentActiveAgentInfo={task.id === activeTaskId ? activeAgentAndModelString : null}
        />
      ))}
    </div>
  );
};

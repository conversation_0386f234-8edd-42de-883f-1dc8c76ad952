
import React, { useState } from 'react';
import { FileNode } from '../types';
import { FolderIcon, FileIcon, ChevronDownIcon, ChevronRightIcon, BeakerIcon } from './Icons';

interface FileExplorerProps {
  files: FileNode[];
  onFileSelect: (file: FileNode) => void;
}

const FileNodeDisplay: React.FC<{ node: FileNode; onFileSelect: (file: FileNode) => void; level?: number }> = ({ node, onFileSelect, level = 0 }) => {
  const [isOpen, setIsOpen] = useState(true); // Folders open by default

  const handleToggle = () => {
    if (node.type === 'folder') {
      setIsOpen(!isOpen);
    }
  };

  const handleSelect = () => {
    onFileSelect(node);
  };

  const indentStyle = { paddingLeft: `${level * 1.25}rem` }; 

  const NodeIcon = () => {
    if (node.type === 'folder') {
      return <FolderIcon className="w-4 h-4 mr-1.5 flex-shrink-0 text-blue-400" />;
    }
    if (node.isTestFile) {
      return <BeakerIcon className="w-4 h-4 mr-1.5 flex-shrink-0 text-teal-400" title="Test File"/>;
    }
    return <FileIcon className="w-4 h-4 mr-1.5 flex-shrink-0 text-yellow-400" />;
  };
  
  const nodeTextColor = node.isTestFile ? 'text-teal-300' : (node.type === 'file' ? 'text-gray-300' : 'text-blue-400');


  return (
    <div>
      <div
        className={`flex items-center p-1.5 rounded-md hover:bg-gray-700 cursor-pointer ${nodeTextColor}`}
        style={indentStyle}
        onClick={node.type === 'file' ? handleSelect : handleToggle}
        onDoubleClick={node.type === 'folder' ? handleToggle : undefined} // Allow double click to toggle folder
        title={node.path}
      >
        {node.type === 'folder' && (
          isOpen ? <ChevronDownIcon className="w-4 h-4 mr-1.5 flex-shrink-0" /> : <ChevronRightIcon className="w-4 h-4 mr-1.5 flex-shrink-0" />
        )}
        <NodeIcon />
        <span className="truncate text-sm">{node.name}</span>
      </div>
      {node.type === 'folder' && isOpen && node.children && (
        <div>
          {node.children.map((child) => (
            <FileNodeDisplay key={child.id} node={child} onFileSelect={onFileSelect} level={level + 1} />
          ))}
        </div>
      )}
    </div>
  );
};


export const FileExplorer: React.FC<FileExplorerProps> = ({ files, onFileSelect }) => {
  if (files.length === 0) {
    return <p className="text-gray-500 text-sm">No file structure generated yet.</p>;
  }

  return (
    <div className="space-y-0.5">
      {files.map((node) => (
        <FileNodeDisplay key={node.id} node={node} onFileSelect={onFileSelect} />
      ))}
    </div>
  );
};


// Heroicons (MIT License) - https://heroicons.com
// Using a subset here. For a real app, consider a library or more organized approach.

import React from 'react';

interface IconProps extends React.SVGProps<SVGSVGElement> {
  title?: string;
}

export const FolderIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z" />
  </svg>
);

export const FileIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
  </svg>
);

export const ChevronDownIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
  </svg>
);

export const ChevronRightIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
  </svg>
);

export const CheckCircleIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
  </svg>
);

export const XCircleIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
  </svg>
);

export const ClockIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
  </svg>
);

export const PlayIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M5.25 5.653c0-.856.935-1.397 1.683-.977l7.108 4.062a1.125 1.125 0 0 1 0 1.953l-7.108 4.062A1.125 1.125 0 0 1 5.25 18.347V5.653Z" />
  </svg>
);

export const InformationCircleIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z" />
  </svg>
);

export const SparklesIcon: React.FC<IconProps> = (props) => (
 <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L1.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09ZM18.25 9.75l1.625-1.625a1.5 1.5 0 012.122 0l1.625 1.625a1.5 1.5 0 010 2.122L21.877 13.5a1.5 1.5 0 01-2.122 0L18.25 11.876a1.5 1.5 0 010-2.122zM18.25 18.25l1.625-1.625a1.5 1.5 0 012.122 0l1.625 1.625a1.5 1.5 0 010 2.122L21.877 20.25a1.5 1.5 0 01-2.122 0L18.25 19.876a1.5 1.5 0 010-2.122z" />
  </svg>
);

export const ExclamationCircleIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
  </svg>
);

export const UserGroupIcon: React.FC<IconProps> = (props) => ( 
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-3.741-5.602M12 12.75a3 3 0 1 0 0-6 3 3 0 0 0 0 6Zm-4.247 3.076a9.094 9.094 0 0 1 3.741-.479 3 3 0 0 1 3.741 5.601M6.247 18.72a9.094 9.094 0 0 1-3.741-.479 3 3 0 0 1 3.741-5.601M12 3.75a9 9 0 1 0 0 18 9 9 0 0 0 0-18Z" />
  </svg>
);

export const MagnifyingGlassIcon: React.FC<IconProps> = (props) => ( 
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
  </svg>
);

export const DocumentTextIcon: React.FC<IconProps> = (props) => ( 
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h4.5m-4.5 3H12M5.25 4.5h.008v.008H5.25V4.5Z" />
    <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 21V5.25A2.25 2.25 0 0 1 6 3h12a2.25 2.25 0 0 1 2.25 2.25v15.75A2.25 2.25 0 0 1 18 21H6a2.25 2.25 0 0 1-2.25-2.25Z" />
  </svg>
);

export const ArchiveBoxIcon: React.FC<IconProps> = (props) => ( 
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 9A1.5 1.5 0 0 1 5.25 7.5h13.5A1.5 1.5 0 0 1 20.25 9v10.5A1.5 1.5 0 0 1 18.75 21H5.25A1.5 1.5 0 0 1 3.75 19.5V9Zm0 0V5.625A2.625 2.625 0 0 1 6.375 3h11.25A2.625 2.625 0 0 1 20.25 5.625V9m-16.5 0h16.5" />
    <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 12h3" />
  </svg>
);

export const ArrowPathIcon: React.FC<IconProps> = (props) => ( // For Refactoring
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99" />
  </svg>
);

export const ShieldExclamationIcon: React.FC<IconProps> = (props) => ( // For Bug Re-check
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m0-10.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.75c0 5.592 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.57-.598-3.75h-.152c-3.196 0-6.1-1.249-8.25-3.286Zm0 12.75h.008v.008H12v-.008Z" />
  </svg>
);

export const BeakerIcon: React.FC<IconProps> = (props) => ( // For Testing/Analysis
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M14.25 2.25v1.088c0 .539.203 1.04.568 1.413l4.91 4.911c.506.506.778 1.196.778 1.924v5.018a2.25 2.25 0 0 1-2.25 2.25H5.25a2.25 2.25 0 0 1-2.25-2.25v-5.018c0-.728.272-1.418.778-1.924l4.91-4.91c.366-.374.568-.874.568-1.413V2.25M14.25 6H9.75m0 0h7.5" />
    <path strokeLinecap="round" strokeLinejoin="round" d="M3 12.75h18" />
  </svg>
);

export const ClipboardDocumentListIcon: React.FC<IconProps> = (props) => ( // For Test Code Writing
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M16.5 3.75V16.5L12 14.25 7.5 16.5V3.75m9 0H18A2.25 2.25 0 0 1 20.25 6v12A2.25 2.25 0 0 1 18 20.25H6A2.25 2.25 0 0 1 3.75 18V6A2.25 2.25 0 0 1 6 3.75h1.5m9 0h-9" />
  </svg>
);

export const LightBulbIcon: React.FC<IconProps> = (props) => ( // For Test Planning
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 18v-5.25m0 0a6.01 6.01 0 0 0 1.5-.189m-1.5.189a6.01 6.01 0 0 1-1.5-.189m3.75 7.478a12.06 12.06 0 0 1-4.5 0m3.75 2.354a15.057 15.057 0 0 1-4.5 0M3 13.5c0 2.397.073 3.369.264 4.143A5.26 5.26 0 0 0 8.25 21h7.5a5.26 5.26 0 0 0 4.986-3.357c.191-.774.264-1.746.264-4.143M12 3v3M3.75 6.75l2.121 2.121M18.129 6.75l-2.121 2.121M12 6.75A2.25 2.25 0 0 1 14.25 9v.75A2.25 2.25 0 0 1 12 12H9.75a2.25 2.25 0 0 1-2.25-2.25V9A2.25 2.25 0 0 1 9.75 6.75h2.25Z" />
  </svg>
);

export const PhotoIcon: React.FC<IconProps> = (props) => ( // For Image Generation
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 15.75l5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z" />
  </svg>
);

export const ArrowUpCircleIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M15 11.25l-3-3m0 0l-3 3m3-3v7.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

export const ArrowDownCircleIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75l3 3m0 0l3-3m-3 3v-7.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

export const ArrowSmallRightIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 12h15m0 0l-6.75-6.75M19.5 12l-6.75 6.75" />
  </svg>
);

export const PuzzlePieceIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M13.5 4.5v2.25A2.25 2.25 0 0015.75 9h2.25m-2.25 0a2.25 2.25 0 002.25-2.25V4.5m0 0A2.25 2.25 0 0015.75 2.25H12A2.25 2.25 0 009.75 4.5M4.5 19.5v-2.25A2.25 2.25 0 002.25 15H.75A.75.75 0 000 15.75v3c0 .414.336.75.75.75h3a.75.75 0 00.75-.75V19.5m0 0h2.25A2.25 2.25 0 009.75 15h.75M19.5 4.5v2.25A2.25 2.25 0 0117.25 9h-4.5A2.25 2.25 0 0110.5 6.75V4.5m0 0A2.25 2.25 0 0112.75 2.25h.75A2.25 2.25 0 0115.75 4.5m0 0v2.25A2.25 2.25 0 0013.5 9h-1.5A2.25 2.25 0 009.75 6.75V4.5m4.5 15v-2.25A2.25 2.25 0 0011.25 15h-1.5A2.25 2.25 0 007.5 17.25V19.5m0 0h2.25A2.25 2.25 0 0012 17.25h.75a2.25 2.25 0 002.25-2.25V13.5A2.25 2.25 0 0012.75 11.25H12A2.25 2.25 0 009.75 13.5m0 0V15A2.25 2.25 0 0012 17.25h.75a2.25 2.25 0 002.25-2.25m-5.25 0A2.25 2.25 0 007.5 15h-1.5A2.25 2.25 0 003.75 17.25V19.5m0 0A2.25 2.25 0 006 21.75h13.5A2.25 2.25 0 0021.75 19.5V18a2.25 2.25 0 00-2.25-2.25h-1.5m-2.25-6.75A2.25 2.25 0 0017.25 12h2.25a.75.75 0 00.75-.75V9A.75.75 0 0021.75 8.25h-3a.75.75 0 00-.75.75v1.5m0 0A2.25 2.25 0 0015.75 12h-1.5m10.5-1.5h-2.25A2.25 2.25 0 0015.75 12h.75M4.5 4.5A2.25 2.25 0 006.75 6.75H9A2.25 2.25 0 0011.25 9V8.25A.75.75 0 0010.5 7.5h-3A.75.75 0 006.75 6.75V4.5m0 0h2.25A2.25 2.25 0 009 6.75V9m-4.5 3A2.25 2.25 0 006.75 11.25H9A2.25 2.25 0 0011.25 13.5v1.5A2.25 2.25 0 009 17.25H6.75A2.25 2.25 0 004.5 15V12m0 0V9A2.25 2.25 0 002.25 6.75H.75A.75.75 0 000 7.5v3c0 .414.336.75.75.75h3A.75.75 0 004.5 10.5V9" />
  </svg>
);

export const ScaleIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 3.75v16.5M4.5 3.75L12 2.25l7.5 1.5M4.5 3.75V7.5c0 .621.504 1.125 1.125 1.125H8.25a1.125 1.125 0 001.125-1.125V3.75M4.5 3.75c-.621 0-1.125.504-1.125 1.125v3.375c0 .621.504 1.125 1.125 1.125H8.25M19.5 3.75L12 2.25l-7.5 1.5M19.5 3.75V7.5c0 .621-.504 1.125-1.125 1.125H15.75a1.125 1.125 0 00-1.125-1.125V3.75M19.5 3.75c.621 0 1.125.504 1.125 1.125v3.375c0 .621-.504 1.125-1.125 1.125H15.75" />
  </svg>
);

export const CodeBracketSquareIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M14.25 9.75L16.5 12l-2.25 2.25m-4.5 0L7.5 12l2.25-2.25M6 20.25h12A2.25 2.25 0 0020.25 18V6A2.25 2.25 0 0018 3.75H6A2.25 2.25 0 003.75 6v12A2.25 2.25 0 006 20.25z" />
  </svg>
);

export const ListBulletIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
  </svg>
);

export const QuestionMarkCircleIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 5.25h.008v.008H12v-.008Z" />
  </svg>
);

export const DatabaseIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6A2.25 2.25 0 016 3.75h12A2.25 2.25 0 0120.25 6v2.25A2.25 2.25 0 0118 10.5H6A2.25 2.25 0 013.75 8.25V6ZM3.75 13.5A2.25 2.25 0 016 11.25h12A2.25 2.25 0 0120.25 13.5v2.25A2.25 2.25 0 0118 18H6a2.25 2.25 0 01-2.25-2.25V13.5Z" />
  </svg>
);

export const TrashIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12.56 0c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
  </svg>
);

export const ArrowRightCircleIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M12.75 15l3-3m0 0l-3-3m3 3h-7.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

export const CheckIcon: React.FC<IconProps> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    {props.title && <title>{props.title}</title>}
    <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 12.75l6 6 9-13.5" />
  </svg>
);

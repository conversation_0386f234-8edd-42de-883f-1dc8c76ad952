
import React from 'react';

interface ProjectProgressBarProps {
  progress: number; // Percentage from 0 to 100
  tasksCompleted: number;
  tasksTotal: number;
}

export const ProjectProgressBar: React.FC<ProjectProgressBarProps> = ({ progress, tasksCompleted, tasksTotal }) => {
  const clampedProgress = Math.max(0, Math.min(100, progress));

  return (
    <div className="w-full" aria-label={`Project progress: ${tasksCompleted} of ${tasksTotal} tasks completed, ${clampedProgress.toFixed(0)}%`}>
      <div className="flex justify-between mb-1 text-xs text-gray-300">
        <span>Overall Progress</span>
        <span>{`${tasksCompleted} / ${tasksTotal} Tasks (${clampedProgress.toFixed(0)}%)`}</span>
      </div>
      <div className="w-full bg-gray-700 rounded-full h-2.5 overflow-hidden">
        <div
          className="bg-gradient-to-r from-purple-500 to-purple-700 h-2.5 rounded-full transition-all duration-500 ease-out"
          style={{ width: `${clampedProgress}%` }}
          role="progressbar"
          aria-valuenow={clampedProgress}
          aria-valuemin={0}
          aria-valuemax={100}
        ></div>
      </div>
    </div>
  );
};

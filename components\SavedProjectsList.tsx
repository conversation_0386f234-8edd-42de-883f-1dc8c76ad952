
import React from 'react';
import { SavedProjectEntry } from '../types';
import { DatabaseIcon, TrashIcon, ArrowRightCircleIcon, DocumentTextIcon, CheckIcon } from './Icons'; // Assuming Icons.tsx

interface SavedProjectsListProps {
  projects: SavedProjectEntry[];
  onLoadProject: (projectId: string) => void;
  onDeleteProject: (projectId: string) => void;
}

export const SavedProjectsList: React.FC<SavedProjectsListProps> = ({ projects, onLoadProject, onDeleteProject }) => {
  if (projects.length === 0) {
    return (
      <div className="text-center text-gray-500 py-8">
        <DatabaseIcon className="w-16 h-16 mx-auto mb-4 text-gray-600" />
        <p className="text-xl">No Saved Projects Yet</p>
        <p className="text-sm">Start by describing your new idea below.</p>
      </div>
    );
  }

  return (
    <div className="w-full max-w-3xl mx-auto">
      <h2 className="text-2xl font-semibold text-purple-300 mb-4 text-center">Your Saved Projects</h2>
      <div className="space-y-3 max-h-[40vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-gray-850">
        {projects.map((project) => (
          <div
            key={project.id}
            className="bg-gray-700/70 p-4 rounded-lg shadow-md flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 hover:bg-gray-600/70 transition-colors duration-150"
            aria-labelledby={`project-name-${project.id}`}
          >
            <div className="flex-grow min-w-0">
              <h3 id={`project-name-${project.id}`} className="text-lg font-medium text-purple-400 truncate" title={project.name}>
                {project.name}
              </h3>
              <p className="text-xs text-gray-400 mt-1 truncate" title={project.ideaSnippet}>
                Idea: {project.ideaSnippet}
              </p>
              <div className="text-xs text-gray-500 mt-1 flex items-center gap-2 flex-wrap">
                <span>Last modified: {new Date(project.lastModified).toLocaleString()}</span>
                <span className="flex items-center" title="Total Tasks">
                  <DocumentTextIcon className="w-3 h-3 mr-0.5" /> {project.taskCount}
                </span>
                <span className="flex items-center" title="Completed Tasks">
                  <CheckIcon className="w-3 h-3 mr-0.5 text-green-400" /> {project.completedTaskCount}
                </span>
              </div>
            </div>
            <div className="flex-shrink-0 flex gap-2 mt-3 sm:mt-0">
              <button
                onClick={() => onLoadProject(project.id)}
                className="bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-3 rounded-md text-sm transition duration-150 ease-in-out flex items-center"
                aria-label={`Load project ${project.name}`}
              >
                <ArrowRightCircleIcon className="w-4 h-4 mr-1.5" /> Load
              </button>
              <button
                onClick={() => onDeleteProject(project.id)}
                className="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-3 rounded-md text-sm transition duration-150 ease-in-out flex items-center"
                aria-label={`Delete project ${project.name}`}
              >
                <TrashIcon className="w-4 h-4 mr-1.5" /> Delete
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

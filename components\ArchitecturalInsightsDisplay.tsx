
import React, { useState } from 'react';
import { DevNote } from '../types';
import { LightBulbIcon, CodeBracketSquareIcon, DocumentTextIcon, ChevronDownIcon, ChevronRightIcon, ShieldExclamationIcon } from './Icons'; // Assuming Icons.tsx exists

interface ArchitecturalInsightsDisplayProps {
  suggestedTechnologyStack?: string;
  architecturalSuggestions: string[];
  architecturalNotes?: string;
  devNotes: DevNote[];
}

const CollapsibleSection: React.FC<{ title: string; icon: React.ReactNode; children: React.ReactNode, defaultOpen?: boolean }> = ({ title, icon, children, defaultOpen = false }) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  return (
    <div className="bg-gray-850 p-3 rounded-md shadow">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between text-left text-sm font-medium text-purple-300 hover:text-purple-200 focus:outline-none"
        aria-expanded={isOpen}
      >
        <span className="flex items-center">
          {icon}
          <span className="ml-2">{title}</span>
        </span>
        {isOpen ? <ChevronDownIcon className="w-4 h-4" /> : <ChevronRightIcon className="w-4 h-4" />}
      </button>
      {isOpen && <div className="mt-2 pl-1 text-xs text-gray-300 space-y-1.5 max-h-40 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-700 scrollbar-track-gray-850">{children}</div>}
    </div>
  );
};

export const ArchitecturalInsightsDisplay: React.FC<ArchitecturalInsightsDisplayProps> = ({
  suggestedTechnologyStack,
  architecturalSuggestions,
  architecturalNotes,
  devNotes,
}) => {
  return (
    <section aria-labelledby="architectural-insights-heading" className="flex-shrink-0 mt-1 mb-1">
      <h2 id="architectural-insights-heading" className="text-md font-semibold text-purple-300 mb-2">
        Project Insights & Notes
      </h2>
      <div className="space-y-2">
        {suggestedTechnologyStack && (
          <div className="bg-gray-850 p-3 rounded-md shadow">
            <h3 className="text-sm font-medium text-purple-300 flex items-center">
              <CodeBracketSquareIcon className="w-4 h-4 mr-2 text-cyan-400" />
              Suggested Technology Stack
            </h3>
            <p className="text-xs text-gray-300 mt-1">{suggestedTechnologyStack}</p>
          </div>
        )}

        {architecturalSuggestions.length > 0 && (
          <CollapsibleSection 
            title={`Architectural Suggestions (${architecturalSuggestions.length})`}
            icon={<LightBulbIcon className="w-4 h-4 text-yellow-400" />}
            defaultOpen={true}
          >
            <ul className="list-disc list-inside pl-2">
              {architecturalSuggestions.map((suggestion, index) => (
                <li key={`arch-sugg-${index}`}>{suggestion}</li>
              ))}
            </ul>
          </CollapsibleSection>
        )}

        {architecturalNotes && (
          <CollapsibleSection 
            title="Architectural Notes Log"
            icon={<DocumentTextIcon className="w-4 h-4 text-indigo-400" />}
          >
            <pre className="whitespace-pre-wrap break-all">{architecturalNotes}</pre>
          </CollapsibleSection>
        )}

        {devNotes.length > 0 && (
          <CollapsibleSection 
            title={`Developer Notes (${devNotes.length})`}
            icon={<ShieldExclamationIcon className="w-4 h-4 text-orange-400" />}
            defaultOpen={true}
          >
            {devNotes.map((note) => (
              <div key={note.id} className="p-1.5 bg-gray-700/50 rounded-sm">
                <p><strong>[{note.source}]</strong> {note.note}</p>
                {note.relatedFilePath && <p className="text-[0.65rem] text-gray-400">File: {note.relatedFilePath}</p>}
                <p className="text-[0.6rem] text-gray-500">{new Date(note.timestamp).toLocaleString()}</p>
              </div>
            ))}
          </CollapsibleSection>
        )}

        { !suggestedTechnologyStack && architecturalSuggestions.length === 0 && !architecturalNotes && devNotes.length === 0 && (
            <p className="text-xs text-gray-500 italic bg-gray-850 p-3 rounded-md shadow">No specific insights or notes recorded yet for this project.</p>
        )}
      </div>
    </section>
  );
};


export const sanitizeFilename = (name: string): string => {
  if (!name) return 'untitled_project';
  // Replace non-alphanumeric (excluding ., -, _) with underscore, convert to lowercase, and limit length
  let sanitized = name.replace(/[^a-z0-9_.-]/gi, '_').toLowerCase();
  // Prevent multiple consecutive underscores
  sanitized = sanitized.replace(/__+/g, '_');
  // Prevent leading/trailing underscores/dots
  sanitized = sanitized.replace(/^[_.-]+|[_.-]+$/g, '');
  // Limit length
  sanitized = sanitized.substring(0, 50);
  if (!sanitized) return 'project'; // Fallback if all chars were stripped
  return sanitized;
};

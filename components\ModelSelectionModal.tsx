
import React, { useState, useEffect } from 'react';
import { ModelInfo, AgentType } from '../types';

interface AgentModelConfigurationModalProps {
  models: ModelInfo[];
  currentConfig: Record<AgentType, string>;
  onSubmit: (newConfig: Record<AgentType, string>) => void;
}

export const AgentModelConfigurationModal: React.FC<AgentModelConfigurationModalProps> = ({ models, currentConfig, onSubmit }) => {
  const [selectedConfig, setSelectedConfig] = useState<Record<AgentType, string>>(currentConfig);

  useEffect(() => {
    // Ensure all agent types, including newly added ones, have a default if not in currentConfig
    const fullConfig = { ...currentConfig };
    let updated = false;
    for (const agentKey of Object.values(AgentType)) {
        if (!fullConfig[agentKey] && models.length > 0) {
            // Default to the first available model if an agent type is missing a config
            // This might happen if new agent types are added and local storage is old
            fullConfig[agent<PERSON>ey] = models[0].id; 
            updated = true;
        }
    }
    if (updated) {
        // If we had to add defaults, update the parent state indirectly via a submit
        // Or, parent should ensure initial currentConfig is always complete
        // For now, just set local state
    }
    setSelectedConfig(fullConfig);
  }, [currentConfig, models]);

  const handleModelChange = (agentType: AgentType, modelId: string) => {
    setSelectedConfig(prevConfig => ({
      ...prevConfig,
      [agentType]: modelId,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Ensure all agent types have a selection before submitting
    const allAgentsConfigured = Object.values(AgentType).every(agentType => selectedConfig[agentType]);
    if (allAgentsConfigured) {
      onSubmit(selectedConfig);
    } else {
      // Handle error: not all agents configured (should not happen with useEffect defaulting)
      console.error("Not all agents have a model configured.");
    }
  };

  const getAgentDisplayName = (agentType: AgentType): string => {
    return agentType.charAt(0).toUpperCase() + agentType.slice(1).toLowerCase().replace(/_/g, ' ');
  }

  return (
    <div className="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 z-50">
      <div className="bg-gray-800 p-8 rounded-lg shadow-2xl w-full max-w-3xl max-h-[90vh] flex flex-col">
        <h2 className="text-2xl font-bold mb-4 text-center text-purple-400">Configure AI Agent Models</h2>
        <p className="text-sm text-gray-400 mb-6 text-center">
          Assign a specific Gemini model to each AI agent. This allows for optimized performance and resource management.
          Your choices will be saved locally.
        </p>
        <form onSubmit={handleSubmit} className="flex-grow overflow-y-auto pr-2 space-y-6">
          {Object.values(AgentType).map((agentType) => (
            <div key={agentType} className="p-4 border border-gray-700 rounded-lg bg-gray-700/30">
              <h3 className="text-lg font-semibold text-purple-300 mb-3">{getAgentDisplayName(agentType)} Agent</h3>
              <select
                id={`model-select-${agentType}`}
                value={selectedConfig[agentType] || (models.length > 0 ? models[0].id : '')} // Fallback if not in selectedConfig
                onChange={(e) => handleModelChange(agentType, e.target.value)}
                className="w-full p-3 bg-gray-700 border border-gray-600 rounded-md text-gray-100 focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none"
                aria-label={`Select model for ${getAgentDisplayName(agentType)} Agent`}
                required
              >
                {models.length === 0 && <option value="">No models available</option>}
                {models.map((model) => (
                  <option key={model.id} value={model.id}>
                    {model.name} (RPM: {model.rpm}, TPM: {model.tpm}, RPD: {model.rpd})
                  </option>
                ))}
              </select>
              {models.find(m => m.id === selectedConfig[agentType])?.description && (
                 <p className="text-xs text-gray-400 mt-2">
                    {models.find(m => m.id === selectedConfig[agentType])?.description}
                 </p>
              )}
            </div>
          ))}
          
          <div className="sticky bottom-0 bg-gray-800 pt-6 pb-1">
            <button
              type="submit"
              className="w-full bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 rounded-md transition duration-150 ease-in-out"
            >
              Confirm Configuration & Proceed
            </button>
          </div>
        </form>
         <p className="text-xs text-gray-500 mt-6 text-center">
          Model details are for informational purposes. Refer to official Google documentation for precise and up-to-date information.
        </p>
      </div>
    </div>
  );
};

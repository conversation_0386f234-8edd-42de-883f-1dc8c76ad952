
import React, { useEffect, useRef } from 'react';
import { AgentLog } from '../types';
import { InformationCircleIcon, CheckCircleIcon, XCircleIcon, SparklesIcon, UserGroupIcon, BeakerIcon } from './Icons';

interface AgentLogFeedProps {
  logs: AgentLog[];
}

const LogIcon: React.FC<{ status: AgentLog['status'], agent: string }> = ({ status, agent }) => {
  if (agent === "Project Manager") return <UserGroupIcon className="w-5 h-5 text-indigo-400" />;
  if (agent.includes("Tester")) return <BeakerIcon className="w-5 h-5 text-teal-400" />;


  switch (status) {
    case 'info':
      return <InformationCircleIcon className="w-5 h-5 text-blue-400" />;
    case 'success':
      return <CheckCircleIcon className="w-5 h-5 text-green-400" />;
    case 'error':
      return <XCircleIcon className="w-5 h-5 text-red-400" />;
    case 'working':
      return <SparklesIcon className="w-5 h-5 text-yellow-400 animate-pulse" />;
    default:
      return <InformationCircleIcon className="w-5 h-5 text-gray-400" />;
  }
};

export const AgentLogFeed: React.FC<AgentLogFeedProps> = ({ logs }) => {
  const logContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (logContainerRef.current) {
      // Scroll to top when new logs are added (since newest are at the top)
      logContainerRef.current.scrollTop = 0;
    }
  }, [logs]);

  if (logs.length === 0) {
    return <p className="text-xs text-gray-500 italic">No company activity recorded yet.</p>;
  }

  return (
    <div 
      ref={logContainerRef} 
      className="space-y-1.5 text-xs max-h-72 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800 pr-1"
    >
      {logs.slice().reverse().map((log) => ( // Display newest logs first
        <div 
            key={log.id} 
            className={`flex items-start gap-2 p-1.5 rounded border-l-2
            ${log.status === 'error' ? 'bg-red-900/30 border-red-700' : 
              log.status === 'success' ? 'bg-green-900/20 border-green-700' :
              log.status === 'working' ? 'bg-yellow-900/20 border-yellow-700' :
              'bg-gray-700/30 border-gray-600'}`}
        >
          <div className="flex-shrink-0 mt-0.5">
            <LogIcon status={log.status} agent={log.agent} />
          </div>
          <div className="flex-grow">
            <span className={`font-semibold ${
                log.agent.includes("Planner") ? "text-cyan-300" :
                log.agent.includes("Coder") ? "text-lime-300" :
                log.agent.includes("Bug Hunter") ? "text-amber-300" :
                log.agent.includes("Refactorer") ? "text-orange-300" :
                log.agent.includes("Context") ? "text-violet-300" :
                log.agent.includes("Tester") ? "text-teal-300" :
                log.agent.includes("Project Manager") ? "text-indigo-300" :
                "text-purple-300"}`}>
                {log.agent}:
            </span>
            <span className="text-gray-300 ml-1">{log.message}</span>
            <span className="ml-2 text-gray-500 text-[0.65rem] float-right">
              {new Date(log.timestamp).toLocaleTimeString()}
            </span>
          </div>
        </div>
      ))}
    </div>
  );
};

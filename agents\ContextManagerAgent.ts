import { GeminiService } from '../services/geminiService';
import { GeminiJsonContextUpdateResponse, LoggingInterface, AgentType } from '../types';

/**
 * ContextManagerAgent handles project context updates and management.
 * Provides comprehensive logging of all context management activities and decisions.
 */
export class ContextManagerAgent {
  private geminiService: GeminiService;
  private logger: LoggingInterface;

  constructor(geminiService: GeminiService, logger: LoggingInterface) {
    if (!geminiService) {
      throw new Error("ContextManagerAgent: GeminiService instance is required.");
    }
    if (!logger) {
      throw new Error("ContextManagerAgent: LoggingInterface instance is required.");
    }
    this.geminiService = geminiService;
    this.logger = logger;
  }

  /**
   * Logs company-level activities for this agent
   */
  private async logActivity(message: string, status: 'info' | 'success' | 'error' | 'working', taskId?: string): Promise<void> {
    await this.logger.addCompanyLog('Context Manager Agent', message, status, taskId);
  }

  /**
   * Logs decision-making activities for this agent
   */
  private logDecision(action: string, details: string, reason?: string, taskId?: string): void {
    this.logger.addDecisionLogEntry(AgentType.CONTEXT_MANAGER, action, details, reason, taskId);
  }

  /**
   * Updates the project context by integrating new information.
   * @param currentContext - The current project context.
   * @param newInformation - New information to integrate into the context.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the updated context response.
   */
  public async updateProjectContext(
    currentContext: string,
    newInformation: string,
    modelName: string
  ): Promise<GeminiJsonContextUpdateResponse> {
    try {
      await this.logActivity(`Updating project context with new information (${newInformation.length} characters)`, 'working');
      this.logDecision('Context Update Started', `Integrating new information: ${newInformation.substring(0, 100)}${newInformation.length > 100 ? '...' : ''}`, `Using model: ${modelName}`);

      const originalPrompt = `
        Current Project Context:
        ---
        ${currentContext}
        ---
        New Information to integrate:
        ---
        ${newInformation}
        ---
        Update the project context.
      `;

      const response = await this.geminiService.makeRequestWithRetry(
        modelName,
        originalPrompt,
        "You are a Context Manager Agent. Update the project context by integrating new information. Return a JSON object with 'updatedContext' property containing the updated context.",
        (data: any): data is GeminiJsonContextUpdateResponse => {
            return typeof data === 'object' && data !== null &&
                   'updatedContext' in data && typeof data.updatedContext === 'string';
        },
        0.3
      );

      await this.logActivity(`Project context updated successfully (${response.updatedContext.length} characters)`, 'success');
      this.logDecision('Context Update Completed', `Updated context to ${response.updatedContext.length} characters`, 'Context integration completed successfully');

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Failed to update project context: ${errorMessage}`, 'error');
      this.logDecision('Context Update Failed', `Error: ${errorMessage}`, 'Context update encountered an error');
      console.error("ContextManagerAgent: Error updating project context -", error);
      throw error;
    }
  }
}

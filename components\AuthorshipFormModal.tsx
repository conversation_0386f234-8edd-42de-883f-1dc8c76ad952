
import React, { useState, useEffect } from 'react';
import { UserAuthorshipDetails } from '../types';

interface AuthorshipFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (details: UserAuthorshipDetails) => void;
}

export const AuthorshipFormModal: React.FC<AuthorshipFormModalProps> = ({ isOpen, onClose, onSubmit }) => {
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [website, setWebsite] = useState('');
  const [copyrightYear, setCopyrightYear] = useState<string>(new Date().getFullYear().toString());
  const [errors, setErrors] = useState<{ fullName?: string; email?: string; copyrightYear?: string }>({});

  useEffect(() => {
    if (isOpen) {
      // Reset form on open, or load existing details if passed in future
      setFullName('');
      setEmail('');
      setWebsite('');
      setCopyrightYear(new Date().getFullYear().toString());
      setErrors({});
    }
  }, [isOpen]);

  const validate = (): boolean => {
    const newErrors: { fullName?: string; email?: string; copyrightYear?: string } = {};
    if (!fullName.trim()) newErrors.fullName = 'Full name is required.';
    if (!email.trim()) {
      newErrors.email = 'Email is required.';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email is invalid.';
    }
    if (!copyrightYear.trim()) {
      newErrors.copyrightYear = 'Copyright year is required.';
    } else if (!/^\d{4}$/.test(copyrightYear)) {
      newErrors.copyrightYear = 'Copyright year must be a 4-digit year.';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validate()) {
      onSubmit({
        fullName: fullName.trim(),
        email: email.trim(),
        website: website.trim() || undefined,
        copyrightYear: copyrightYear.trim(),
      });
    }
  };

  if (!isOpen) {
    return null;
  }

  return (
    <div 
      className="fixed inset-0 bg-gray-900 bg-opacity-80 flex items-center justify-center p-4 z-50"
      role="dialog"
      aria-modal="true"
      aria-labelledby="authorship-form-modal-title"
    >
      <div className="bg-gray-800 p-6 sm:p-8 rounded-lg shadow-2xl w-full max-w-md border border-purple-600">
        <h2 id="authorship-form-modal-title" className="text-xl sm:text-2xl font-bold mb-4 text-center text-purple-400">Proprietary License Authorship</h2>
        <p className="text-sm text-gray-300 mb-6 text-center">
          Please provide your authorship details. This information will be used in your project's LICENSE file and potentially in source code headers.
        </p>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="fullName" className="block text-sm font-medium text-gray-300 mb-1">Full Name / Company Name</label>
            <input
              type="text"
              id="fullName"
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
              className={`w-full p-2.5 bg-gray-700 border rounded-md text-gray-100 text-sm focus:ring-1 outline-none ${errors.fullName ? 'border-red-500 focus:ring-red-500' : 'border-gray-600 focus:ring-purple-500'}`}
              required
              aria-describedby={errors.fullName ? "fullName-error" : undefined}
            />
            {errors.fullName && <p id="fullName-error" className="text-xs text-red-400 mt-1">{errors.fullName}</p>}
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">Contact Email</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className={`w-full p-2.5 bg-gray-700 border rounded-md text-gray-100 text-sm focus:ring-1 outline-none ${errors.email ? 'border-red-500 focus:ring-red-500' : 'border-gray-600 focus:ring-purple-500'}`}
              required
              aria-describedby={errors.email ? "email-error" : undefined}
            />
            {errors.email && <p id="email-error" className="text-xs text-red-400 mt-1">{errors.email}</p>}
          </div>

          <div>
            <label htmlFor="website" className="block text-sm font-medium text-gray-300 mb-1">Website (Optional)</label>
            <input
              type="url"
              id="website"
              value={website}
              onChange={(e) => setWebsite(e.target.value)}
              placeholder="https://example.com"
              className="w-full p-2.5 bg-gray-700 border border-gray-600 rounded-md text-gray-100 text-sm focus:ring-1 focus:ring-purple-500 outline-none"
            />
          </div>
          
          <div>
            <label htmlFor="copyrightYear" className="block text-sm font-medium text-gray-300 mb-1">Copyright Year</label>
            <input
              type="text"
              id="copyrightYear"
              value={copyrightYear}
              onChange={(e) => setCopyrightYear(e.target.value)}
              maxLength={4}
              className={`w-full p-2.5 bg-gray-700 border rounded-md text-gray-100 text-sm focus:ring-1 outline-none ${errors.copyrightYear ? 'border-red-500 focus:ring-red-500' : 'border-gray-600 focus:ring-purple-500'}`}
              required
              aria-describedby={errors.copyrightYear ? "copyrightYear-error" : undefined}
            />
            {errors.copyrightYear && <p id="copyrightYear-error" className="text-xs text-red-400 mt-1">{errors.copyrightYear}</p>}
          </div>

          <div className="flex flex-col sm:flex-row gap-3 pt-2">
            <button
              type="button"
              onClick={onClose}
              className="w-full sm:flex-1 bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2.5 px-4 rounded-md text-sm transition duration-150 ease-in-out"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="w-full sm:flex-1 bg-purple-600 hover:bg-purple-700 text-white font-semibold py-2.5 px-4 rounded-md text-sm transition duration-150 ease-in-out"
            >
              Save Authorship & Proceed
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

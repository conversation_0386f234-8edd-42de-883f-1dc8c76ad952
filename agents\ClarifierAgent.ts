
import { GeminiService } from '../services/geminiService';
import { <PERSON><PERSON>ontext, GeminiJsonClarifierResponse, LoggingInterface, AgentType } from '../types';
import { SYSTEM_INSTRUCTION_CLARIFIER } from '../constants';

/**
 * ClarifierAgent handles clarification requests from other agents.
 * Provides comprehensive logging of all clarification activities and decisions.
 */
export class ClarifierAgent {
  private geminiService: GeminiService;
  private logger: LoggingInterface;

  constructor(geminiService: GeminiService, logger: LoggingInterface) {
    if (!geminiService) {
      throw new Error("ClarifierAgent: GeminiService instance is required.");
    }
    if (!logger) {
      throw new Error("ClarifierAgent: LoggingInterface instance is required.");
    }
    this.geminiService = geminiService;
    this.logger = logger;
  }

  /**
   * Logs company-level activities for this agent
   */
  private async logActivity(message: string, status: 'info' | 'success' | 'error' | 'working', taskId?: string): Promise<void> {
    await this.logger.addCompanyLog('Clarifier Agent', message, status, taskId);
  }

  /**
   * Logs decision-making activities for this agent
   */
  private logDecision(action: string, details: string, reason?: string, taskId?: string): void {
    this.logger.addDecisionLogEntry(AgentType.CLARIFIER, action, details, reason, taskId);
  }

  /**
   * Provides clarification or makes a decision based on a question and project context.
   * This agent simulates user input or expert knowledge to unblock other agents.
   * @param question - The question posed by another agent.
   * @param projectContext - The current full context of the project.
   * @param modelName - The name of the Gemini model to use for clarification.
   * @returns A promise that resolves to the clarifier's answer.
   */
  public async clarify(
    question: string,
    projectContext: ProjectContext,
    modelName: string
  ): Promise<GeminiJsonClarifierResponse> {
    try {
      await this.logActivity(`Received clarification request: "${question.substring(0, 100)}${question.length > 100 ? '...' : ''}"`, 'working');
      this.logDecision('Clarification Request Received', `Question: ${question.substring(0, 200)}${question.length > 200 ? '...' : ''}`, `From project: ${projectContext.name}`);

      const response = await this.getClarification(question, projectContext, modelName);

      await this.logActivity(`Provided clarification with confidence ${response.confidence || 'unknown'}`, 'success');
      this.logDecision('Clarification Provided', `Answer: ${response.answer.substring(0, 200)}${response.answer.length > 200 ? '...' : ''}`, `Confidence: ${response.confidence || 'unknown'}`);

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      await this.logActivity(`Failed to provide clarification: ${errorMessage}`, 'error');
      this.logDecision('Clarification Failed', `Error: ${errorMessage}`, 'Providing fallback response');

      console.error(`ClarifierAgent: Error during clarification for question "${question}" -`, error);
      // Fallback response in case of error to maintain autonomy
      const fallbackResponse = {
        answer: `Error in ClarifierAgent: Could not get clarification for question: "${question.substring(0, 100)}...". Reason: ${errorMessage}. Attempting to proceed with a default or inferred behavior if possible.`,
        confidence: 0.1
      };

      await this.logActivity(`Provided fallback clarification response`, 'info');
      return fallbackResponse;
    }
  }

  /**
   * Internal method to get clarification from AI model
   */
  private async getClarification(
    question: string,
    projectContext: ProjectContext,
    modelName: string
  ): Promise<GeminiJsonClarifierResponse> {
    const originalPrompt = `
      Full Project Context:
      ---
      Project ID: ${projectContext.id}
      Project Name: ${projectContext.name}
      Original Idea: ${projectContext.idea}
      License: ${projectContext.licenseInfo?.type || 'Unspecified'}
      ${projectContext.licenseInfo?.authorship ? `Author: ${projectContext.licenseInfo.authorship.fullName}` : ''}
      Suggested Technology Stack: ${projectContext.suggestedTechnologyStack || 'Not yet defined'}
      Current Phase: ${projectContext.projectLifecycle}
      Current File Structure Overview:
      ${this.serializeFileStructureForPrompt(projectContext.fileStructure)}
      ---
      Full Context String (long-term memory dump):
      ${projectContext.fullContext.substring(0, 2000)}...
      ---
      Specific Question from another AI Agent:
      "${question}"
      ---
      Based on all the above context and the question, provide a concise, actionable answer.
      If the information is not explicit, make a reasonable inference or common-sense assumption.
      Avoid saying "I don't know." Your role is to enable the other agent to proceed.
    `;

    return this.geminiService.makeRequestWithRetry(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_CLARIFIER,
      (data: any): data is GeminiJsonClarifierResponse => {
        return typeof data === 'object' && data !== null &&
               'answer' in data && typeof data.answer === 'string' &&
               (data.confidence === null || typeof data.confidence === 'undefined' || typeof data.confidence === 'number');
      },
      0.6
    );
  }

  /**
   * Helper method to serialize file structure for prompt
   */
  private serializeFileStructureForPrompt(fileStructure: any[], includeContent: boolean = false): string {
    let structureString = "Project File Structure Overview:\n";
    const traverse = (nodes: any[], depth: number) => {
      for (const node of nodes) {
        let typeInfo = node.type;
        if (node.isTestFile) typeInfo += ' (test file)';

        structureString += `${"  ".repeat(depth)}- ${node.path} (${typeInfo}${node.type === 'file' && !node.content ? ', empty' : node.type === 'file' && node.content ? ', has content' : ''})\n`;
        if (includeContent && node.type === 'file' && node.content) {
            const previewContent = node.content.substring(0, 300) + (node.content.length > 300 ? "..." : "");
            structureString += `${"  ".repeat(depth+1)}Content Preview:\n${"  ".repeat(depth+2)}${previewContent.split('\n').join(`\n${"  ".repeat(depth+2)}`)}\n`;
        }
        if (node.children && node.children.length > 0) {
          traverse(node.children, depth + 1);
        }
      }
    };
    traverse(fileStructure, 0);
    return structureString;
  }
}


import React, { useRef, useEffect } from 'react';
import { DecisionLogEntry, AgentType } from '../types'; // Assuming types are in ../types
import { ListBulletIcon, UserGroupIcon, ClockIcon, SparklesIcon, LightBulbIcon, CodeBracketSquareIcon, ShieldExclamationIcon, MagnifyingGlassIcon, ArrowPathIcon, ArchiveBoxIcon, BeakerIcon, PhotoIcon } from './Icons'; // Assuming Icons.tsx

interface DecisionLogDisplayProps {
  decisionLog: DecisionLogEntry[];
}

// Helper function to get display name for agents (can be moved to a shared util if used elsewhere)
const getAgentDisplayName = (agentType: AgentType | 'System' | 'ProjectManager' | 'User'): string => {
  if (agentType === 'System' || agentType === 'ProjectManager' || agentType === 'User') {
    return agentType;
  }
  // For AgentType enum values
  return agentType.charAt(0).toUpperCase() + agentType.slice(1).toLowerCase().replace(/_/g, ' ');
};


const AgentIcon: React.FC<{ agent: AgentType | 'System' | 'ProjectManager' | 'User' }> = ({ agent }) => {
  switch (agent) {
    case AgentType.PLANNER:
      return <LightBulbIcon className="w-4 h-4 text-cyan-400" title="Planner Agent" />;
    case AgentType.CODER:
      return <CodeBracketSquareIcon className="w-4 h-4 text-lime-400" title="Coder Agent" />;
    case AgentType.BUG_HUNTER:
      return <MagnifyingGlassIcon className="w-4 h-4 text-yellow-400" title="Bug Hunter Agent" />;
    case AgentType.REFACTORER:
      return <ArrowPathIcon className="w-4 h-4 text-orange-400" title="Refactorer Agent" />;
    case AgentType.CONTEXT_MANAGER:
      return <ArchiveBoxIcon className="w-4 h-4 text-indigo-400" title="Context Manager Agent" />;
    case AgentType.TESTER:
      return <BeakerIcon className="w-4 h-4 text-teal-400" title="Tester Agent" />;
    case AgentType.IMAGE_GENERATOR:
      return <PhotoIcon className="w-4 h-4 text-pink-400" title="Image Generator Agent" />;
    case AgentType.CLARIFIER:
      return <SparklesIcon className="w-4 h-4 text-purple-400" title="Clarifier Agent" />;
    case AgentType.LINTER_FORMATTER:
      return <SparklesIcon className="w-4 h-4 text-gray-400" title="Linter/Formatter Agent" />; // Consider a more specific icon
    case AgentType.SECURITY_ANALYST:
      return <ShieldExclamationIcon className="w-4 h-4 text-red-400" title="Security Analyst Agent" />;
    case 'System':
      return <ClockIcon className="w-4 h-4 text-gray-400" title="System" />; // Or specific system icon
    case 'ProjectManager':
      return <UserGroupIcon className="w-4 h-4 text-blue-400" title="Project Manager" />;
    case 'User':
      return <UserGroupIcon className="w-4 h-4 text-green-400" title="User" />; // Or specific user icon
    default:
      return <ListBulletIcon className="w-4 h-4 text-gray-500" />;
  }
};


export const DecisionLogDisplay: React.FC<DecisionLogDisplayProps> = ({ decisionLog }) => {
  const scrollableContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (scrollableContainerRef.current) {
      // Scroll to top when new decision logs are added (since newest are at the top)
      scrollableContainerRef.current.scrollTop = 0;
    }
  }, [decisionLog]);

  if (!decisionLog || decisionLog.length === 0) {
    return (
      <section aria-labelledby="decision-log-heading" className="flex-shrink-0 mt-1 mb-1">
        <h2 id="decision-log-heading" className="text-md font-semibold text-purple-300 mb-2">
          Decision Log
        </h2>
        <div className="bg-gray-850 p-3 rounded-md shadow">
           <p className="text-xs text-gray-500 italic">No decisions recorded yet.</p>
        </div>
      </section>
    );
  }

  return (
    <section aria-labelledby="decision-log-heading" className="flex-shrink-0 mt-1 mb-1">
      <h2 id="decision-log-heading" className="text-md font-semibold text-purple-300 mb-2">
        Decision Log ({decisionLog.length})
      </h2>
      <div 
        ref={scrollableContainerRef}
        className="bg-gray-850 p-3 rounded-md shadow max-h-60 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800 space-y-2 pr-1"
      >
        {decisionLog.slice().reverse().map((entry) => (
          <div key={entry.id} className="text-xs p-1.5 bg-gray-700/50 rounded-sm break-words">
            <div className="flex items-center justify-between mb-0.5">
              <span className="font-semibold text-purple-300 flex items-center">
                <AgentIcon agent={entry.agent} />
                <span className="ml-1.5">{getAgentDisplayName(entry.agent)}</span>
              </span>
              <span className="text-[0.65rem] text-gray-500">{new Date(entry.timestamp).toLocaleString()}</span>
            </div>
            <p className="text-gray-300"><strong className="text-gray-400">Action:</strong> {entry.action}</p>
            <p className="text-gray-400 text-[0.7rem]"><strong className="text-gray-500">Details:</strong> {entry.details.substring(0, 250)}{entry.details.length > 250 ? '...' : ''}</p>
            {entry.reason && <p className="text-gray-400 text-[0.7rem] italic"><strong className="text-gray-500">Reason:</strong> {entry.reason}</p>}
            {entry.taskId && <p className="text-gray-500 text-[0.65rem]">Task ID: {entry.taskId}</p>}
          </div>
        ))}
      </div>
    </section>
  );
};

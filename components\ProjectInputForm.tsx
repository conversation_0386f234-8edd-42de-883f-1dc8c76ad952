
import React, { useState } from 'react';

interface ProjectInputFormProps {
  onSubmit: (projectIdea: string) => void;
  isLoading: boolean;
}

export const ProjectInputForm: React.FC<ProjectInputFormProps> = ({ onSubmit, isLoading }) => {
  const [projectIdea, setProjectIdea] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (projectIdea.trim() && !isLoading) {
      onSubmit(projectIdea.trim());
    }
  };

  return (
    <div className="flex flex-col items-center justify-center h-full p-4">
      <div className="bg-gray-800 p-8 rounded-lg shadow-2xl w-full max-w-2xl">
        <h2 className="text-3xl font-bold mb-6 text-center text-purple-400">Start a New Project</h2>
        <p className="text-gray-400 mb-6 text-center">
          Describe the application you want to build. The more details, the better.
        </p>
        <form onSubmit={handleSubmit}>
          <textarea
            value={projectIdea}
            onChange={(e) => setProjectIdea(e.target.value)}
            placeholder="e.g., A full-stack task management app with user authentication and categories. Tech: React, Node.js, PostgreSQL."
            className="w-full p-4 mb-6 bg-gray-700 border border-gray-600 rounded-md text-gray-100 focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none h-40 resize-none"
            required
            disabled={isLoading}
            aria-label="Project idea description"
          />
          <button
            type="submit"
            className="w-full bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 rounded-md transition duration-150 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isLoading || !projectIdea.trim()}
          >
            {isLoading ? 'Creating Project & Generating Plan...' : 'Create & Start Building'}
          </button>
        </form>
      </div>
    </div>
  );
};